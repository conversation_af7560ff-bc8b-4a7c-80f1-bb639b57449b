# 排课管理页面 (scheduleClasses.vue)

## 功能概述

这是一个类似苹果日历界面的排课管理页面，提供了完整的课程排课功能。

## 主要功能

### 1. 日历视图
- **月份导航**: 支持前后月份切换，可快速回到今天
- **日期高亮**: 当天日期会特殊高亮显示
- **课程显示**: 每个日期格子中显示当天的课程安排
- **跨天课程**: 支持跨多天的课程以连续横条形式显示

### 2. 课程管理
- **新增排课**: 点击"新增排课"按钮可创建新课程
- **编辑课程**: 点击未来的课程可进行编辑
- **查看课程**: 点击已开始或已结束的课程可查看详情（只读模式）
- **删除课程**: 右键点击未开始的课程可删除
- **权限控制**: 已开始或已结束的课程无法编辑/删除，但可以查看

### 3. 课程信息
每个课程包含以下信息：
- 课程名称
- 任课老师
- 开始时间
- 结束时间  
- 作业提交截止时间
- 课程小组成员

### 4. 小组管理
- **多小组支持**: 每个课程可包含多个小组
- **成员管理**: 每个小组可添加多个成员
- **动态操作**: 支持动态添加/删除小组和成员

## 技术实现

### 前端技术栈
- **Vue 3**: 使用 Composition API
- **Element Plus**: UI 组件库
- **Moment.js**: 时间处理
- **SCSS**: 样式预处理

### 数据结构
```typescript
interface ClassInfoInput {
    id: String
    startTime: Timestamp        // 开始时间
    endTime: Timestamp          // 结束时间
    submitDeadline: Timestamp   // 作业提交截止时间
    teacher: String             // 任课老师ID
    teacherName: String         // 任课老师姓名
    courseName: String          // 课程名称
    groupUsers: [GroupUserInput] // 课程小组成员
}

interface GroupUserInput {
    id: String
    createdAt: Timestamp
    updatedAt: Timestamp
    users: [UserInput]
    groupIndex: Int
}
```

### API 接口 (伪代码)
- `getClassList(startTime, endTime)`: 根据日期范围获取课程列表
- `createClass`: 创建新课程
- `updateClass`: 更新课程信息
- `deleteClass`: 删除课程

### 数据获取优化
- **按需加载**: 只获取当前日历页面显示日期范围内的课程数据
- **自动刷新**: 当用户切换月份时，自动重新获取对应日期范围的课程数据
- **性能优化**: 避免重复计算，使用计算属性缓存按日期分组的课程数据

## 页面特色

### 1. 苹果日历风格
- 简洁的月视图布局
- 优雅的日期导航
- 直观的课程展示

### 2. 跨天课程支持
- 自动识别跨天课程
- 连续横条显示，类似苹果日历的跨天事件
- 起始和结束标识
- 视觉连贯性，课程条目在相邻日期格子间无缝连接

### 3. 交互体验
- 悬停效果
- 点击编辑
- 右键删除
- 表单验证

### 4. 响应式设计
- 适配不同屏幕尺寸
- 灵活的网格布局
- 优化的移动端体验

## 使用说明

### 访问路径
```
/manage/scheduleClasses
```

### 基本操作
1. **查看课程**: 在日历中查看已排课程
2. **新增课程**: 点击右上角"新增排课"按钮
3. **编辑课程**: 点击未来的课程进行编辑
4. **查看历史课程**: 点击已开始或已结束的课程查看详情（只读模式）
5. **删除课程**: 右键点击未开始的课程选择删除
6. **导航日期**: 使用左右箭头切换月份，点击"今天"回到当前日期

### 表单填写
1. 填写课程基本信息（名称、老师、时间等）
2. 设置课程小组和成员
3. 验证表单信息
4. 保存课程

## 注意事项

1. **时间限制**: 只能编辑/删除未开始的课程
2. **表单验证**: 结束时间必须晚于开始时间
3. **小组管理**: 每个课程至少需要一个小组和一个成员
4. **数据持久化**: 当前使用模拟数据，实际使用时需要连接真实API

## 样式定制

页面样式定义在 `ide/src/assets/style/style.scss` 文件中的 `.schedule-classes-wrap` 部分，可根据需要进行定制。

## 扩展功能

未来可以考虑添加：
- 周视图和日视图
- 课程搜索和筛选
- 批量操作
- 课程模板
- 导入导出功能
- 课程冲突检测
