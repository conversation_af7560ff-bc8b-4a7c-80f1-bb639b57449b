# 排课管理页面 - 数据获取逻辑优化

## 问题分析

原始实现中存在的问题：
1. `createDayObject` 方法中调用 `getClassesForDay` 导致重复计算
2. 每次创建日期对象时都要遍历所有课程数据进行过滤
3. 没有根据日历页面的日期范围来获取数据，可能加载不必要的数据

## 优化方案

### 1. 按日历页面日期范围获取数据

```javascript
// 计算属性：当前日历页面的日期范围
const calendarDateRange = computed(() => {
    const year = currentDate.value.getFullYear();
    const month = currentDate.value.getMonth();
    
    // 获取当月第一天
    const firstDay = new Date(year, month, 1);
    const firstDayWeek = firstDay.getDay();
    
    // 计算日历页面的第一天（可能是上个月的日期）
    const calendarStartDate = new Date(year, month - 1, new Date(year, month - 1, 0).getDate() - firstDayWeek + 1);
    
    // 计算日历页面的最后一天（42天后）
    const calendarEndDate = new Date(calendarStartDate);
    calendarEndDate.setDate(calendarStartDate.getDate() + 41);
    
    return {
        startDate: calendarStartDate,
        endDate: calendarEndDate,
        startTimestamp: calendarStartDate.getTime(),
        endTimestamp: calendarEndDate.getTime()
    };
});
```

### 2. 预处理课程数据按日期分组

```javascript
// 计算属性：按日期分组的课程数据
const classesByDate = computed(() => {
    const dateMap = new Map();
    
    classes.value.forEach(classItem => {
        const startTime = moment(classItem.startTime);
        const endTime = moment(classItem.endTime);
        
        // 获取课程跨越的所有日期
        const currentDate = moment(startTime).startOf('day');
        const endDate = moment(endTime).startOf('day');
        
        while (currentDate.isSameOrBefore(endDate)) {
            const dateKey = currentDate.format('YYYY-MM-DD');
            
            if (!dateMap.has(dateKey)) {
                dateMap.set(dateKey, []);
            }
            
            // 判断跨天课程的显示状态
            const isStart = startTime.isSame(currentDate, 'day');
            const isEnd = endTime.isSame(currentDate, 'day');
            const isMiddle = !isStart && !isEnd;
            const isSpan = !startTime.isSame(endTime, 'day');
            
            dateMap.get(dateKey).push({
                ...classItem,
                isStart,
                isEnd,
                isMiddle,
                isSpan
            });
            
            currentDate.add(1, 'day');
        }
    });
    
    return dateMap;
});
```

### 3. 简化日期对象创建

```javascript
// 创建日期对象
const createDayObject = (date, day, isOtherMonth) => {
    const dateKey = moment(date).format('YYYY-MM-DD');
    const isToday = moment(date).isSame(today, 'day');
    
    // 从预处理的课程数据中获取当天的课程
    const dayClasses = classesByDate.value.get(dateKey) || [];
    
    return {
        date,
        day,
        dateKey,
        isToday,
        isOtherMonth,
        classes: dayClasses
    };
};
```

### 4. 自动监听日期范围变化

```javascript
// 监听日历日期范围变化
watch(calendarDateRange, (newRange, oldRange) => {
    // 只有当日期范围真正改变时才重新获取数据
    if (!oldRange || 
        newRange.startTimestamp !== oldRange.startTimestamp || 
        newRange.endTimestamp !== oldRange.endTimestamp) {
        
        console.log('日历日期范围变化，重新获取课程数据:', {
            old: oldRange ? {
                start: new Date(oldRange.startTimestamp).toLocaleDateString(),
                end: new Date(oldRange.endTimestamp).toLocaleDateString()
            } : null,
            new: {
                start: new Date(newRange.startTimestamp).toLocaleDateString(),
                end: new Date(newRange.endTimestamp).toLocaleDateString()
            }
        });
        
        fetchClassList(newRange.startTimestamp, newRange.endTimestamp);
    }
}, { immediate: true });
```

## 优化效果

### 性能提升
1. **减少API调用**: 只在日期范围变化时才获取数据
2. **避免重复计算**: 使用计算属性缓存按日期分组的数据
3. **提高响应速度**: 预处理数据，创建日期对象时直接获取

### 用户体验
1. **按需加载**: 只加载当前页面需要的数据
2. **自动刷新**: 切换月份时自动获取新数据
3. **实时更新**: 数据变化时界面自动更新

### 数据流程

```
用户操作 → 改变 currentDate → 触发 calendarDateRange 计算
    ↓
calendarDateRange 变化 → 触发 watch → 调用 fetchClassList
    ↓
获取新数据 → 更新 classes → 触发 classesByDate 计算
    ↓
classesByDate 变化 → 触发 calendarDays 计算 → 界面更新
```

## API 接口设计

```javascript
// 获取指定日期范围的课程列表
const fetchClassList = async (startTimestamp, endTimestamp) => {
    // GraphQL 查询示例
    const result = await apolloClient.query({
        query: gql`
            query getClassList($startTime: Timestamp!, $endTime: Timestamp!) {
                classList(startTime: $startTime, endTime: $endTime) {
                    id
                    courseName
                    teacherName
                    teacher
                    startTime
                    endTime
                    submitDeadline
                    groupUsers {
                        id
                        groupIndex
                        users {
                            id
                            name
                            openId
                        }
                    }
                }
            }
        `,
        variables: {
            startTime: startTimestamp,
            endTime: endTimestamp
        }
    });
    
    classes.value = result.data.classList;
};
```

## 总结

通过这些优化：
1. 消除了 `createDayObject` 中的重复计算
2. 实现了按需加载，提高了性能
3. 简化了数据流程，提高了代码可维护性
4. 提供了更好的用户体验

这种设计模式可以应用到其他类似的日历或时间相关的组件中。
