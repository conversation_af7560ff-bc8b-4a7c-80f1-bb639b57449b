:root {
    --el-color-primary: #AD0404;
    --el-color-primary-light-3: #C43C3C;
    /* 混合30%白色 */
    --el-color-primary-light-5: #D67F7F;
    /* 混合50%白色 */
    --el-color-primary-light-7: #E9C2C2;
    /* 混合70%白色 */
    --el-color-primary-light-8: #F1D8D8;
    /* 混合80%白色 */
    --el-color-primary-light-9: #F8EDED;
    /* 混合90%白色 */
    --el-color-primary-dark-2: #8B0303;
    /* 混合20%黑色 */
    --el-slider-button-size: 15px;
}

@mixin scrollbar {
    &::-webkit-scrollbar {
        /*滚动条整体样式*/
        background-color: transparent;
        border-radius: 10px;
        width: 5px;
        height: 12px;
        z-index: 100;
    }
    &::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius: 10px;
        background: rgba(143, 143, 143, 0.4);
    }
    &::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        border-radius: 10px;
        background: transparent;
    }
}

// flex 布局 上下居中
@mixin flex-center {
    display: flex;
    align-items: center;
    // justify-content: space-between;
}

@mixin squarestate {
    >span {
        margin-right: 3px;
        display: inline-block;
        width: 10px;
        height: 10px;
        background: var(--el-color-success-light-5);
        &.del {
            background: var(--el-color-danger-light-5);
        }
        &.edit {
            background: var(--el-color-warning-light-5);
        }
    }
}

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

.price-wrap {
    height: 100%;
    &.front-index {
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: center;
        font-size: 1.5rem;
        color: #666;
        .fi-img {
            img {
                width: 420px;
                margin-top: -200px;
            }
        }
    }
    .iframe-erea {
        height: calc(100% - 52px);
        overflow: auto;
        .data-inner {
            height: calc(100% - 6px);
        }
    }
}

.unselectable {
    -webkit-user-select: none;
    /* Safari */
    -moz-user-select: none;
    /* Firefox */
    -ms-user-select: none;
    /* IE10+/Edge */
    user-select: none;
    /* Standard */
}

.tab-list {
    display: flex;
    border-radius: 12px 12px 0 0;
    background-color: #e2e8f8;
    overflow: hidden;
    &.new-style {
        justify-content: center;
        border-radius: 0;
        background-color: transparent;
        .tab-item {
            flex: none;
            max-width: 100%;
            height: initial;
            border: 2px solid #bc3935;
            margin: 0 10px;
            border-radius: 20px;
            color: #bc3935;
            opacity: 1;
            box-shadow: none;
            font-weight: normal;
            .tab-inner {
                width: 100%;
                padding: 7px 20px;
                font-size: 0.8rem;
                &:hover {
                    background: transparent;
                }
                &::after {
                    display: none;
                }
            }
            &.tab-selected {
                background: #bc3935;
                color: #fff;
                box-shadow: none;
            }
        }
        .tab-selected {
            &::after,
            &::before {
                display: none;
            }
        }
    }
    .tab-item {
        flex: 1;
        max-width: 200px;
        height: 52px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 15px;
        opacity: 0.65;
        font-weight: 600;
        position: relative;
        cursor: pointer;
        .tab-inner {
            display: flex;
            height: 70%;
            align-items: center;
            width: 90%;
            justify-content: center;
            border-radius: 5px;
            // transition: background 200ms ease-in;
            &:hover {
                background: #c8d4f6;
            }
        }
        &.tab-plain {
            .tab-inner {
                display: relative;
                &:after {
                    position: absolute;
                    right: 0;
                    content: "";
                    width: 2px;
                    height: 20px;
                    background: #6b77d3;
                }
            }
            &:nth-last-of-type(1) {
                .tab-inner {
                    &:after {
                        width: 0;
                    }
                }
            }
        }
    }
    .tab-icon {
        width: 17px;
        height: 17px;
        margin-right: 4px;
    }
    .tab-selected {
        opacity: 1;
        background: #fff;
        border-radius: 12px 12px 0 0;
        box-shadow: 12px 12px 0 0 #fff, -12px 12px 0 0 #fff;
        .tab-inner {
            &:hover {
                background: #fff;
            }
        }
        &::before {
            content: '';
            position: absolute;
            left: -12px;
            bottom: 0;
            width: 12px;
            height: 52px;
            background: #e2e8f8;
            border-radius: 0 0 12px 0;
        }
        &::after {
            content: '';
            position: absolute;
            right: -12px;
            bottom: 0;
            width: 12px;
            height: 52px;
            background: #e2e8f8;
            border-radius: 0 0 0 12px;
        }
    }
}

.stepview-wrap {
    height: 100vh;
    background: #fff;
    .step-content {
        height: calc(100% - 70px);
        display: flex;
        .sc-menu {
            width: 200px;
            .el-menu {
                height: 100%;
            }
        }
        .sc-content {
            width: calc(100% - 200px);
            &.no-side {
                width: calc(100%);
            }
            .data-inner {
                height: calc(100% - 52px);
                &.no-tabs {
                    height: 100%;
                }
            }
        }
    }
}

.comparison-header {
    // margin-bottom: 40px;
    // text-align: center;
    .title {
        font-size: 28px;
        font-weight: 600;
        color: #1d1d1f;
        margin-bottom: 20px;
    }
}

.hotel-comparison {
    /* max-width: 1200px; */
    // height: 100%;
    margin: 0 auto;
    padding: 20px;
    .competitor-selector {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
    }
    .competitor-selector select {
        padding: 8px 12px;
        border: 1px solid #d2d2d7;
        border-radius: 8px;
        font-size: 16px;
        background-color: white;
        outline: none;
    }
    .comparison-body {
        display: flex;
        /* grid-template-columns: 1fr  1fr; */
        gap: 20px;
        // overflow: auto;
        height: calc(100% - 80px);
        .el-carousel {
            width: 100%;
        }
    }
}

.comparison-column {
    display: flex;
    justify-content: center;
    min-width: 350px;
    max-width: 350px;
    // height: 100%;
    .hotel-card {
        width: 100%;
        min-width: 350px;
        max-width: 350px;
        border: 1px solid #e5e5e5;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }
    .our-hotel .hotel-card {
        border-color: #0070c9;
        box-shadow: 0 4px 12px rgba(0, 112, 201, 0.1);
    }
    .hotel-info {
        padding: 20px;
        // background-color: #fafafa;
        background: linear-gradient(to bottom, #f7ebea, #ffffff);
        border-bottom: 1px solid #e5e5e5;
        height: 80px;
    }
    .our-hotel .hotel-info {
        background-color: rgba(0, 112, 201, 0.05);
    }
    .hotel-name {
        font-size: 22px;
        font-weight: 600;
        color: #1d1d1f;
        margin-bottom: 8px;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: default;
    }
    .price {
        font-size: 20px;
        // color: #0070c9;
        color: var(--el-color-primary);
        font-weight: 500;
        margin-bottom: 8px;
    }
    .location {
        font-size: 14px;
        color: #6e6e73;
        line-height: 1.4;
    }
    .comparison-items {
        padding: 20px;
    }
    .comparison-item {
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f5f5f7;
    }
    .comparison-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }
    .item-title {
        font-size: 16px;
        font-weight: 500;
        color: #1d1d1f;
        margin-bottom: 6px;
    }
    .sub-title {
        font-size: 14px;
        font-weight: 400;
        color: #6e6e73;
        margin-top: 2px;
    }
    .item-value {
        font-size: 16px;
        color: #333;
        white-space: pre-line;
    }
    .comparison-divider {
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .vs-badge {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: #f5f5f7;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 20px;
        font-weight: 600;
        color: #6e6e73;
    }
    /* 响应式调整 */
    @media (max-width: 768px) {
        .comparison-body {
            grid-template-columns: 1fr;
        }
        .comparison-divider {
            padding: 20px 0;
        }
        .vs-badge {
            transform: rotate(90deg);
        }
    }
    .nested-item {
        margin-bottom: 8px;
    }
    .nested-key {
        font-weight: 500;
        color: #6e6e73;
        margin-bottom: 2px;
        font-size: 14px;
        padding-left: 8px;
    }
    .nested-value {
        padding-left: 24px;
    }
    ul {
        list-style-type: disc;
        padding-left: 20px;
        margin: 0;
    }
    li {
        margin-bottom: 4px;
        &.editable {
            .add {
                color: var(--el-color-success);
            }
            .del {
                color: var(--el-color-danger);
            }
            .el-icon {
                display: none;
                cursor: pointer;
                +.el-icon {
                    margin-left: 5px;
                }
            }
            &:hover {
                .el-icon {
                    display: initial;
                }
            }
        }
    }
    .editable {
        // cursor: pointer;
        cursor: url('../images/edit.png'), auto;
    }
    .editable:hover {
        text-decoration: underline;
    }
}

.toggle-menu {
    display: inline-flex;
    padding: 8px;
    background: var(--el-color-primary);
    border-radius: 5px;
    cursor: pointer;
    position: fixed;
    z-index: 99;
    left: 1rem;
    bottom: 20px;
    // transition: left 500ms ease-in-out;
    transition: transform 0.1s ease, left 0.3s ease, top 0.3s ease;
    /* 添加拖拽时的视觉效果 */
    user-select: none;
    &.hassidemenu {
        left: 150px;
    }
    &:active {
        transform: scale(0.95);
    }
}

.silde-comparison {
    position: relative;
    .nav-button {
        position: absolute;
        width: 36px;
        height: 36px;
        background: rgba(255, 255, 255, 0.8);
        color: #606266;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        z-index: 10;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: all 0.3s;
        box-shadow: 0 0 6px rgba(0, 0, 0, 0.12);
        top: calc(50vh - 18px);
    }
    .nav-button svg {
        width: 20px;
        height: 20px;
    }
    .nav-button:hover {
        background: white;
        color: #409EFF;
    }
    .button-visible {
        opacity: 1;
    }
    .left {
        left: 16px;
    }
    .right {
        right: 16px;
    }
}

.text-area-edit {
    padding: 20px;
    .text-operation {
        margin-top: 10px;
        text-align: center;
    }
}

.price-chart-wrap {
    height: 100vh;
    padding: 20px;
    //
    .price-chart-inner-wrap {
        // display: flex;
        height: calc(100%);
        // overflow: auto;
        &.multi-version {
            height: calc(100% - 45px);
        }
        .excel-tables {
            height: 35%;
            overflow: auto;
        }
        .chart-show {
            height: calc(100% - 35%);
        }
    }
    .chart-show {
        height: calc(100%);
        width: 100%;
    }
}

.excel-area {
    display: inline-block;
    // margin: 10px;
    height: 100%;
    .row-header {
        display: flex;
        min-width: 300px;
        .header-item {
            flex: 1;
            text-align: center;
            line-height: 3;
            background: var(--el-color-primary-light-3);
            color: #fff;
            border-top-left-radius: 10px;
            font-weight: bold;
            font-size: 1.1rem;
            +.header-item {
                border-top-left-radius: 0;
                border-top-right-radius: 10px;
            }
        }
    }
    .row-content {
        height: calc(100% - 20px - 60px);
        // overflow-x: auto;
    }
    .content-single-row {
        display: flex;
        min-width: 300px;
        // border-bottom: 1px dashed var(--el-border-color-dark);
        // background: var(--el-color-primary-light-9);
        // background: var(--el-fill-color-light);
        &:nth-of-type(2n) {
            // background: var(--el-color-primary-light-9);
        }
        &:nth-last-of-type(1) {
            border-bottom: none;
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
        }
        .row-column {
            flex: 1;
            text-align: center;
            line-height: 2.8;
            color: var(--el-text-color-regular);
            border-bottom: 1px dashed var(--el-border-color-dark);
            background: var(--el-fill-color-light);
            cursor: pointer;
            &:hover {
                // background: var(--el-color-primary);
                // border-radius: 5px;
                // color: #fff;
            }
            .edit-input {
                width: 100%;
                height: 100%;
                outline: none;
                text-align: center;
                padding: 0 5px;
                box-sizing: border-box;
            }
        }
    }
}

.price-chart-edit-wrap {
    .excel-area {
        width: 100%;
        .row-header {
            .header-item {
                border-radius: 0;
                &:nth-of-type(1) {
                    border-top-left-radius: 10px;
                }
                &:nth-last-of-type(1) {
                    border-top-right-radius: 10px;
                }
            }
        }
    }
}

.row-edit {
    .el-icon {
        background-color: #fff;
        border-radius: 50%;
        color: var(--el-color-success);
        +.el-icon {
            margin-left: 5px;
            color: var(--el-color-warning);
        }
    }
}

.differentiation-container {
    margin: 0 auto;
    padding: 20px;
    .container {
        // max-width: 800px;
    }
    .item-card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        cursor: pointer;
        >label {
            cursor: pointer;
        }
        &.item-card-checked {
            border-color: var(--el-color-primary);
            background-color: var(--el-color-primary-light-9);
            .item-property {
                background: #fff;
            }
        }
        .item-theme {
            // margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .base {
                display: flex;
                align-items: center;
            }
        }
        .item-content {
            margin-top: 10px;
        }
        .label-title {
            font-weight: bold;
            &.able-to-change {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding-right: 6px;
            }
        }
    }
    .item-property {
        margin: 10px 0;
        padding: 8px;
        background-color: #f5f5f5;
        border-radius: 4px;
        .value-item-property {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            border: 1px dashed #666;
            border-radius: 5px;
            padding: 5px;
            margin-top: 5px;
            .single-item {
                flex: 1;
                margin-right: 10px;
            }
            &:nth-last-of-type(1) {
                // margin-bottom: 0;
            }
            .value-value-item-label {
                font-size: 0.8rem;
                font-weight: bold;
                color: var(--el-color-primary-light-5);
                margin-right: 5px;
                margin-top: 4px;
                min-width: 60px;
            }
            .value-value-item-property {
                display: flex;
                align-items: flex-start;
                margin-bottom: 5px;
                float: left;
                width: 48%;
                &.value-value-item-property-full {
                    width: 100%;
                }
            }
        }
    }
    h2 {
        color: #333;
    }
    h3 {
        color: #444;
        margin-bottom: 15px;
    }
    pre {
        background-color: #f8f8f8;
        padding: 15px;
        border-radius: 4px;
        white-space: pre-wrap;
        word-wrap: break-word;
    }
    input[type="checkbox"] {
        margin-right: 8px;
    }
    .differentiation-ope {
        text-align: center;
        margin-top: 20px;
    }
    .selected-items-container {
        border: 1px solid #e1e1e1;
        border-radius: 8px;
        padding: 15px;
        background-color: #f9f9f9;
        margin-top: 10px;
        // display: flex;
    }
    .empty-message {
        color: #888;
        text-align: center;
        padding: 20px;
    }
    .selected-item {
        background-color: white;
        border-radius: 6px;
        padding: 12px;
        margin-bottom: 10px;
        // margin-right: 10px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    .selected-item-header {
        display: flex;
        align-items: center;
        // margin-bottom: 8px;
        // padding-bottom: 8px;
        // border-bottom: 1px dashed #eee;
    }
    .item-index {
        background-color: #f0f7ff;
        color: #1a73e8;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.85em;
        margin-right: 10px;
    }
    .property-key {
        // font-weight: bold;
        flex-grow: 1;
        color: #333;
    }
    .remove-btn {
        background: none;
        border: none;
        color: #f44336;
        font-size: 1.2em;
        cursor: pointer;
        padding: 0 5px;
        line-height: 1;
    }
    .remove-btn:hover {
        color: #d32f2f;
        transform: scale(1.2);
    }
    .selected-item-value {
        color: #555;
        line-height: 1.5;
        white-space: pre-wrap;
        word-break: break-word;
    }
}

.carousel-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 20px;
    .carousel-wrapper {
        position: relative;
        display: flex;
        align-items: center;
    }
    .carousel {
        display: flex;
        overflow-x: auto;
        scroll-behavior: smooth;
        scrollbar-width: none;
        /* Firefox */
        -ms-overflow-style: none;
        /* IE and Edge */
        gap: 20px;
        padding: 10px 0;
        flex: 1;
    }
    .carousel::-webkit-scrollbar {
        display: none;
    }
    .nav-btns-wrap {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        .nav-button {
            width: 30px;
            height: 30px;
            &.disabled {
                opacity: 0.5;
                cursor: not-allowed;
                border-color: #666;
                color: #666;
            }
        }
        .left {
            margin-right: 5px;
        }
        .right {
            margin-left: 5px;
        }
    }
    .nav-button {
        background: #fff;
        border: 2px solid var(--el-color-primary);
        color: var(--el-color-primary);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        font-size: 20px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        z-index: 10;
        flex-shrink: 0;
        // position: absolute;
        // top: 30%;
    }
    .nav-button:hover:not(.disabled) {
        background: var(--el-color-primary);
        color: white;
    }
}

.version-change {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    .el-select {
        width: 230px;
    }
}

.cost-pricing-wrap {
    padding: 20px;
    height: 100%;
    .category-header {
        display: flex;
        gap: 20px;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem;
        background: var(--el-color-primary-light-3);
        border-bottom: 1px solid #e9ecef;
        color: #fff;
        .edit-mode {
            .edit-input {
                width: 80%;
            }
        }
        .view-mode {
            .category-name {
                color: #fff;
            }
        }
    }
    .category-card {
        // width: 330px;
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
        margin-bottom: 20px;
        overflow: hidden;
        transition: all 0.3s ease;
        &:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12);
        }
    }
    .category-actions,
    .item-ope {
        display: flex;
        gap: 10px;
        .el-icon {
            cursor: pointer;
            color: var(--el-color-success);
            +.el-icon {
                color: var(--el-color-warning);
            }
        }
    }
    .view-mode {
        flex-grow: 1;
        display: block;
        cursor: pointer;
        // padding: 0.5rem 0;
        color: var(--el-text-color-regular);
        .item-text {
            font-size: 0.9rem;
        }
        .item-value {
            font-size: 1.2rem;
        }
    }
    .edit-mode {
        flex-grow: 1;
        display: flex;
        gap: 0.5rem;
        .el-input {
            width: 120px;
        }
    }
    .category-name {
        font-weight: 600;
        font-size: 1.1rem;
        margin-right: 1rem;
    }
    .item-count {
        font-size: 0.85rem;
        color: #6c757d;
        background: #e9ecef;
        padding: 0.2rem 0.5rem;
        border-radius: 10px;
    }
    .category-actions {
        display: flex;
        gap: 10px;
        .el-icon {
            background: #fff;
            border-radius: 3px;
            cursor: pointer;
            width: 20px;
            height: 20px;
            color: var(--el-color-primary);
        }
        .del-catagory,
        .add-catagory {
            color: var(--el-color-primary) !important;
        }
        // .arrow-icon {
        //     transition: transform 0.3s ease-in-out;
        //     transform-origin: center;
        // }
        // .arrow-down {
        //     transform: rotate(0deg);
        // }
        // .arrow-up {
        //     transform: rotate(180deg);
        // }
    }
    .category-items {
        padding: 0.5rem;
    }
    .item-card {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0;
        border-bottom: 1px solid #f1f1f1;
        margin-bottom: 10px;
        padding-bottom: 10px;
        gap: 10px;
        // cursor: pointer;
        &:last-child {
            // border-bottom: none;
            margin-bottom: 0;
        }
        .view-mode {
            flex-grow: 1;
            // display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }
    }
    .icon-btn {
        background: none;
        border: none;
        font-size: 1rem;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 50%;
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s;
        &:hover {
            background: #f1f1f1;
        }
        &.danger:hover {
            background: #ffecec;
            color: #dc3545;
        }
    }
    .add-btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: var(--el-color-primary-light-3);
        border: 1px dashed #adb5bd;
        color: #495057;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.2s;
        margin-top: 0.5rem;
        &:hover {
            background: #e9ecef;
            border-color: #6c757d;
        }
        span {
            font-size: 1.2rem;
        }
        .primary {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            margin: 1.5rem auto;
            display: block;
            &:hover {
                background: #0069d9;
            }
        }
    }
    .data-preview {
        margin-top: 3rem;
        padding: 1.5rem;
        background: var(--el-color-primary-light-3);
        border-radius: 10px;
        h3 {
            margin-top: 0;
            color: #495057;
            font-size: 1.1rem;
        }
    }
    .cost-content {
        height: calc(100% - 50px);
        overflow: hidden;
        display: flex;
        width: 100%;
    }
    .category-card-wrap {
        padding-right: 10px;
    }
    .cost-pricing-result {
        padding-left: 10px;
    }
    .category-card-wrap,
    .cost-pricing-result {
        height: 100%;
        overflow: auto;
        // box-sizing: border-box;
        // padding: 16px;
    }
    .scroll-content {
        height: calc(100% - 32px);
        /* 减去padding */
        overflow: auto;
    }
    .category-actions {}
}

.drag-bar {
    width: 2px;
    height: 100%;
    background-color: var(--el-color-primary-light-3);
    cursor: col-resize;
    transition: background-color 0.2s;
}

.drag-bar:hover {
    width: 4px;
    background-color: var(--el-color-primary-dark-2);
}

.drag-bar:active {
    width: 4px;
    background-color: var(--el-color-primary-dark-2);
}

.generate-word {
    margin: 10px 0;
}

.el-loading-mask {
    min-height: 500px;
}

.atypica-wrap {
    margin: 0 auto;
    padding: 20px;
    height: 100%;
    .iframe-atypica-inner {
        height: calc(100% - 42px);
    }
    .atypica-not-started {
        height: calc(100% - 42px);
        .atypica-textarea {
            height: calc(100% - 32px);
            .at-btn {
                margin-top: 1rem;
                text-align: center;
            }
            .el-textarea {
                height: 100%;
                .el-textarea__inner {
                    height: 100%;
                }
            }
        }
    }
}

.financial-statements-wrap,
.market-segments-wrap {
    height: 100%;
    padding: 20px;
    .excel-area {
        display: block;
        height: calc(100% - 32px - 10px);
        // padding: 10px;
        overflow: auto;
        .row-header {
            .header-item {
                min-width: 120px;
                border-top-left-radius: 0;
                border-top-right-radius: 0;
                &:nth-of-type(1) {
                    border-top-left-radius: 10px;
                }
                &:nth-last-of-type(1) {
                    border-top-right-radius: 10px;
                }
                &.consumer_profile {
                    flex: 2;
                }
                &.header-item-price {
                    min-width: 150px;
                    // max-width: 150px;
                }
                &.consumer_profile {
                    min-width: 250px;
                    // max-width: 250px;
                }
                &.market_size {
                    min-width: 120px;
                    // max-width: 120px;
                }
                &.header-item-operate-aaa {
                    min-width: 100px;
                    max-width: 100px;
                }
            }
        }
        .row-column {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            &.row-column-name {
                line-height: 1.2;
            }
            .edit-able {
                //
                // padding: ;
            }
            &.row-column-consumer_profile {
                min-width: 250px;
                // max-width: 250px;
            }
            &.row-column-market_percent,
            &.row-column-market_size {
                min-width: 120px;
                // max-width: 120px;
            }
            &.row-column-price {
                display: block;
                min-width: 150px;
                // max-width: 150px;
                .edit-able {
                    height: 100%;
                    .edit-able-price {
                        height: 100%;
                        >div {
                            flex: 1;
                        }
                    }
                }
            }
            &.row-edit {
                min-width: 120px;
                &.row-edit-aaa {
                    min-width: 100px;
                    max-width: 100px;
                }
            }
        }
        .row-column-consumer_profile {
            &.row-column-consumer_profile {
                flex: 2;
            }
            .column-label {
                font-weight: bold;
                &.values-add {
                    font-size: 0.7rem;
                }
            }
            .column-description {
                font-size: 0.8rem;
                line-height: 1.2;
                margin-bottom: 10px;
            }
        }
    }
}

.column-label-innder {
    display: flex;
    align-items: center;
    gap: 5px;
    .value-key-inner-del {
        display: flex;
        align-items: center;
        padding-left: 5px;
        color: var(--el-color-warning);
    }
}

.chart-inner {
    height: calc(100%);
    width: 100%;
}

.column-label {
    &.values-add {
        font-size: 0.7rem;
        line-height: 1.2;
        margin: 10px 0;
        text-decoration: underline;
        color: var(--el-color-primary);
    }
}

.market-segments-withchart-wrap {
    &.market-segments-wrap {
        .excel-area {
            height: calc(60% - 32px - 10px)
        }
        .chart-area {
            height: 40%;
        }
    }
}

.financial-statements-wrap {
    .content-inner-wrap {
        display: flex;
        height: 100%;
        .drag-bar {
            margin: 0 5px;
        }
        // .iframe-inner-wrap {
        //     flex: 2;
        // }
        // .excel-area {
        //     flex: 3;
        // }
    }
    .excel-total-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .ana-types-change {
            width: 200px;
        }
        .excel-info {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            >div {
                margin-left: 20px;
            }
        }
    }
}

.chart-inner {
    height: calc(100%);
    width: 100%;
}

.header-inner-others {
    display: flex;
    align-items: center;
    height: 100%;
    justify-content: center;
}

.header-inner-types {
    display: flex;
    line-height: 2;
    font-size: 0.8rem;
    align-items: center;
    justify-content: space-around;
}

.edit-able-price {
    display: flex;
    align-items: center;
    justify-content: space-around;
}

.header-inner-label {
    cursor: pointer;
    flex: 1;
    text-align: center;
    line-height: 3;
    background: var(--el-color-primary-light-3);
    color: #fff;
    border-top-left-radius: 10px;
    font-weight: bold;
    font-size: 1.1rem;
    display: inline-flex;
    align-items: center;
}

.slide-val {
    display: flex;
    justify-content: space-between;
    align-items: center;
    >div {
        // flex: 1;
        width: 45%;
    }
    .slide-per {
        // display: flex;
        // align-items: center;
        // width: 100%;
        // justify-content: space-around;
        >div {
            display: flex;
            align-items: center;
            // margin-bottom: 15px;
            justify-content: space-between;
            // width: 48%;
            margin-bottom: 15px;
            >div {
                width: 85px;
                font-size: 0.8rem;
                font-weight: bold;
                +div {
                    width: calc(100% - 85px);
                }
            }
        }
        .el-slider__button {
            width: 15px;
            height: 15px;
        }
        .el-slider__marks-text {
            color: var(--el-color-info);
            font-size: 12px;
            margin-top: 18px;
            position: absolute;
            transform: translateX(-50%);
            white-space: pre;
            margin-left: 5px;
        }
        .final {
            margin-bottom: 0;
            .el-slider__marks-text {
                color: transparent;
            }
        }
    }
}

.header-adder {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: normal;
    font-size: 0.9rem;
    border-bottom: transparent 1px solid;
    cursor: pointer;
    &:hover {
        border-bottom-color: #fff;
    }
    .el-icon {
        margin-right: 5px;
    }
}

.component-market-segment {
    &.market-segments-wrap {
        padding: 0;
    }
}

.cost-pricing-content {
    height: 100%;
    &.multi-version {
        height: calc(100% - 45px);
    }
    .component-market-segment {
        height: calc(35%);
        &.component-market-segment-hide {
            height: 0;
        }
        // height: initial;
        .excel-area .row-header .header-item {
            line-height: 2;
            font-size: 1rem;
        }
        .header-inner-label {
            line-height: 2;
            font-size: 1rem;
        }
    }
    .chart-slider-wrap {
        height: calc(65%);
        &.chart-slider-full-wrap {
            height: 100%;
        }
    }
    .chart-inner {
        // height: calc(100% - 40px + 20px);
        height: calc(100% - 80px + 20px);
        // min-height: 120px;
    }
}

.excel-tables {
    display: flex;
    .excel-tabs {
        width: 90px;
    }
    .price-chart-edit-inner-wrap {
        flex: 1;
        .excel-area {
            display: block;
        }
        .row-header {
            .header-item+.header-item {
                border-top-right-radius: 0;
                &:nth-last-of-type(1) {
                    border-top-right-radius: 10px;
                }
            }
        }
    }
}

.item-property-title {
    display: flex;
}

.brainstorming-container {
    .item-property {
        margin: 0;
    }
    .item-card {
        padding: 10px;
        .value-item-property {
            border: none;
        }
    }
}

.value-item-property-wrap {
    // display: flex;
    // justify-content: space-between;
    .value-item-property {
        // width: 48%;
    }
}

.single-item-ope {
    min-width: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .el-icon {
        cursor: pointer;
        color: var(--el-color-danger);
        &.add {
            color: var(--el-color-success);
        }
    }
}

.header-item {
    &.header-item-operate-aaa {
        width: 100px;
    }
}

.step-tabs {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 25px 0;
    .tab-item {
        cursor: pointer;
        &.hasdata {
            .tab-item-inner {
                .tii-label {
                    color: var(--el-color-primary);
                    .tii-icon {
                        border-color: var(--el-color-primary);
                    }
                }
                .tii-line {
                    background: var(--el-color-primary);
                }
            }
        }
        &.active {
            .tab-item-inner {
                .tii-label {
                    color: var(--el-color-primary);
                }
                .tii-icon {
                    border-color: var(--el-color-primary);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    &::before {
                        width: 10px;
                        height: 10px;
                        border-radius: 50%;
                        content: "";
                        display: inline-block;
                        background: var(--el-color-primary)
                    }
                }
            }
        }
        .tab-item-inner {
            display: flex;
            align-items: center;
            gap: 20px;
            .tii-label {
                display: flex;
                align-items: center;
                font-size: 0.8rem;
                color: #666;
                // width: 120px;
            }
            .tii-line {
                width: calc((100vw - 640px) / 3);
                // width: 100%;
                margin-right: 30px;
                height: 1px;
                background: #ccc;
            }
        }
        .tii-icon {
            width: 20px;
            height: 20px;
            border: 2px solid #ccc;
            border-radius: 50%;
            margin-right: 10px;
        }
        &:nth-last-child(1) {
            .tii-line {
                display: none;
            }
        }
    }
}

.brainstorming-bot-container {
    height: 100vh;
    .bot-list-wrap,
    .iframe-erea {
        height: calc(100% - 10px);
        &.multi-version {
            height: calc(100% - 42px);
        }
    }
    .bl-title {
        font-size: 1.5rem;
        text-align: center;
        margin-bottom: 2rem;
    }
    .bot-list {
        height: calc(100% - 4rem);
        &::after {
            clear: both;
            content: "";
        }
        .single-bot-item {
            width: calc((100% - 150px) / 4);
            margin-bottom: 30px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            box-shadow: 0 0 10px #ddd;
            transition: all 0.3s ease;
            animation: breathing 3s ease-in-out infinite;
            cursor: pointer;
            margin-right: 50px;
            float: left;
            &:nth-of-type(4n) {
                margin-right: 0;
            }
            &:hover {
                box-shadow: 0 0 15px rgba(100, 149, 237);
                transform: translateY(-5px);
            }
        }
        .sinbot-des {}
    }
}

@keyframes breathing {
    0%,
    100% {
        transform: scale(1);
        // box-shadow: 0 0 8px rgba(221, 221, 221, 0.6);
    }
    50% {
        transform: scale(1.05);
        // box-shadow: 0 0 12px rgba(221, 221, 221, 0.8);
    }
}

.pricing-model {
    margin: 0 auto;
    // padding: 10px;
    // text-align: center;
    height: 100%;
    display: flex;
    .index-welcome {
        width: 40%;
        border-right: 2px solid #ddd;
        display: flex;
        flex-direction: column;
        min-width: 450px;
        overflow: hidden;
        .index-welcome-header {
            position: sticky;
            top: 0;
            width: 100%;
            z-index: 100;
            background: #fefefe;
            padding: 14px 18px;
            // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .c-header {
                font-size: 1.2rem;
                font-weight: bold;
            }
            .c-useinfo {
                color: #666;
                font-size: 0.9rem;
                display: inline-flex;
                align-items: center;
                .el-avatar {
                    margin-right: 10px;
                    background-color: #53a37f;
                }
            }
        }
        .index-welcome-content {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
        }
        .index-welcome-footer {
            position: sticky;
            bottom: 0;
            width: 100%;
            z-index: 100;
            padding: 1rem;
            text-align: center;
            box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
            .el-button {
                width: 100%;
                padding: 1.5rem;
                &[disabled] {
                    opacity: 0.6;
                    cursor: not-allowed;
                }
            }
        }
    }
    .triangle-container {
        flex: 1;
        background: #f5f5f5;
        overflow: auto;
    }
    .cc-menu-item {
        border: 1px solid #666;
        border-bottom-width: 5px;
        border-radius: 5px;
        margin-bottom: 10px;
        cursor: default;
        background: #fff;
        width: 250px;
        padding: 10px;
        height: 85px;
        &.ccm-top {
            border-color: #4a6fa5;
            .ccm-title {
                color: #4a6fa5;
            }
        }
        &.ccm-right {
            border-color: #9c5a7b;
            .ccm-title {
                color: #9c5a7b;
            }
        }
        &.ccm-left {
            border-color: #5a8f6b;
            .ccm-title {
                color: #5a8f6b;
            }
        }
        .ccm-title {
            display: flex;
            align-items: center;
            color: #333;
            line-height: 2;
            &:nth-last-child(1) {
                margin-bottom: 0;
            }
            .img-icon {
                width: 17px;
                // border: 2px solid #b1b1b1;
                border-radius: 50%;
                margin-right: 5px;
            }
        }
        .ccm-des {
            font-size: 0.8rem;
            color: #666;
            text-align: left;
        }
    }
    .corner-content-top {
        position: relative;
        .cc-menu-item {
            position: absolute;
            left: -400%;
            top: -300%;
            &:nth-of-type(2) {
                top: calc(-300% + 85px + 20px);
            }
            &:nth-of-type(3) {
                top: calc(-300% + 85px + 20px + 85px + 20px);
            }
        }
    }
    .corner-content-right {
        position: relative;
        .cc-menu-item {
            position: absolute;
            left: 150%;
            top: -800%;
            height: 110px;
            &:nth-of-type(2) {
                top: calc(-800% + 110px + 20px);
                height: 80px;
            }
            &:nth-of-type(3) {
                top: calc(-800% + 110px + 20px + 80px + 20px);
            }
            &:nth-of-type(4) {
                top: calc(-800% + 110px + 20px + 80px + 20px + 110px + 20px);
            }
        }
    }
    .corner-content-left {
        position: relative;
        .cc-menu-item {
            position: absolute;
            left: -350%;
            top: -35%;
            height: 80px;
            &:nth-of-type(2) {
                top: calc(-800% + 110px + 20px);
                height: 80px;
            }
            &:nth-of-type(3) {
                top: calc(-800% + 110px + 20px + 80px + 20px);
            }
            &:nth-of-type(4) {
                top: calc(-800% + 110px + 20px + 80px + 20px + 110px + 20px);
            }
        }
    }
    .triangle-fill {
        fill: rgba(100, 149, 237, 0.4);
    }
    .title {
        color: #333;
        margin-bottom: 40px;
        font-weight: 600;
    }
    .triangle-container {
        position: relative;
        width: 100%;
        height: 100%;
    }
    .corner {
        position: absolute;
        width: 120px;
        height: 120px;
        background: #4a6fa5;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;
        z-index: 2;
        cursor: pointer;
        &:hover {
            // transform: scale(1.1);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
        }
        .corner-content {
            padding: 10px;
            text-align: center;
        }
        &.top {
            top: 15%;
            left: 50%;
            transform: translateX(-50%);
            background: #4a6fa5;
        }
        &.left {
            bottom: 15%;
            left: 30%;
            background: #5a8f6b;
        }
        &.right {
            bottom: 15%;
            right: 30%;
            background: #9c5a7b;
        }
    }
    /* SVG连线样式 */
    .connectors {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
    }
    .connector-line {
        stroke: #1a76c0;
        stroke-width: 5;
        &.connector-line-rtl {
            stroke: #9c5a7b;
        }
        &.connector-line-ltt {
            stroke: #5a8f6b;
        }
    }
    .connector-text {
        font-size: 1.2rem;
        fill: #1a76c0;
        font-weight: bold;
    }
    .center-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 60%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 15px;
        .inner-item {
            background: #f0f0f0;
            padding: 12px 20px;
            border-radius: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
            width: 80%;
            max-width: 200px;
            &:hover {
                background: #e0e0e0;
                transform: translateY(-2px);
            }
        }
    }
    .index-bth {
        position: absolute;
        right: 20px;
        top: 20px;
    }
    .cc-welcome {
        text-align: center;
        font-size: 1.5rem;
        margin-bottom: 3rem;
        font-weight: 700;
    }
    .iwc-wrap {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        flex-wrap: wrap;
        .iwc-item {
            width: 48%;
            background: #fff;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 0 5px #ddd;
            margin-bottom: 1.2rem;
            cursor: pointer;
            &:hover {
                background: var(--el-color-primary-light-9);
            }
            &.disabled {
                cursor: not-allowed;
                background: #f0f0f0;
            }
            &.choosed {
                background: var(--el-color-primary-light-7);
            }
            .i-title {
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                font-weight: bold;
                margin-bottom: 10px;
                font-size: 0.9rem;
                height: 42px;
            }
            .i-image {
                img {
                    width: 100%;
                }
            }
            .i-detail {
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                height: 48px;
                font-size: 0.7rem;
            }
        }
    }
    .file-detail {
        margin-top: 2rem;
        .fi-title {
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        .fi-image {
            margin-bottom: 10px;
            img {
                width: 100%;
            }
        }
        .fi-detail {
            font-size: 0.8rem;
            color: #666;
            text-indent: 1rem;
        }
    }
}

.diff-chart-show {
    width: 80%;
    height: 500px;
    margin: 0 auto;
}

.competitive-diamension-wrap {
    height: 100%;
    padding: 20px;
    .empty {
        height: 70%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: center;
        font-size: 2rem;
        .auto-btn {
            margin-top: 2rem;
        }
    }
    .dimension-fields {
        h2 {
            text-align: center;
            margin-bottom: 2rem;
        }
        >div {
            margin-bottom: 2rem;
        }
        .df-btn {
            margin: 0;
            margin-top: 3rem;
            text-align: center;
        }
        .el-tag {
            margin-right: 20px;
            margin-bottom: 20px;
        }
    }
}

.role-tag-wrap {
    height: calc(100% - 42px);
    .role-tag-part {
        height: 30%;
        display: flex;
        justify-content: space-between;
        &.el-loading-parent--relative {
            .el-loading-mask {
                min-height: 100%;
            }
        }
        .role-part,
        .tag-part {
            height: 100%;
            width: 48%;
            // padding: 10px;
            padding-top: 0;
            // border: 1px solid #ddd;
            border-radius: 5px;
            background: #ececec47;
            // border-top: 15px solid var(--el-color-primary-light-3);
            .el-input {
                position: relative;
                // top: -10px;
            }
            .tag-area {
                padding: 10px;
                height: calc(100% - 40px);
                overflow: auto;
                >div {
                    display: inline-block;
                }
                .el-tag {
                    margin-bottom: 10px;
                    margin-right: 10px;
                    cursor: pointer;
                }
            }
        }
    }
    .role-tag-tochat {
        margin-top: 10px;
        text-align: center;
    }
    .role-tag-component {
        height: calc(70% - 4rem);
        margin-top: 2rem;
        padding: 1rem;
        border: 1px solid var(--el-color-primary-dark-2);
        border-radius: 5px;
        background: var(--el-color-primary-light-9);
        >.el-tag {
            margin-bottom: 20px;
            margin-right: 20px;
        }
        .role-tag-title {
            font-size: 0.8rem;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .role-com-wrap {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            height: calc(100% - 32px - 20px);
            overflow: auto;
            &::after {
                content: "";
                clear: both;
            }
            .role-card-item {
                border-radius: 5px;
                border: 1px solid #ddd;
                padding: 10px;
                background: #fff;
                position: relative;
                height: min-content;
                min-width: 400px;
                @media screen and (max-width: 960px) {
                    width: 100%;
                }
                @media (min-width: 961px) and (max-width: 1379px) {
                    width: 48%;
                }
                @media screen and (min-width: 1380px) {
                    width: 32%;
                }
                .base-info {
                    display: flex;
                    align-items: center;
                }
                .base-desc {
                    margin-top: 10px;
                    // text-indent: 2rem;
                    font-size: 0.8rem;
                    color: #666;
                    .show-btn {
                        text-align: center;
                    }
                }
                .r-del {
                    position: absolute;
                    z-index: 99;
                    top: 0;
                    left: 0;
                    width: 25px;
                    height: 25px;
                    color: var(--el-color-danger);
                    background: #f4f4f4;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                }
                .r-avater {
                    width: 80px;
                    min-width: 80px;
                    height: 80px;
                    position: relative;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    >.el-icon {
                        display: none;
                        position: absolute;
                        font-size: 30px;
                        width: 100%;
                        height: 100%;
                        background: #dddddd8c;
                        color: var(--el-color-danger);
                        border-radius: 50%;
                        cursor: pointer;
                    }
                    &:hover {
                        >.el-icon {
                            display: inline-flex;
                            z-index: 9;
                        }
                    }
                    >img {
                        width: 90%;
                        border-radius: 50%;
                    }
                }
                .r-info {
                    flex: 1;
                    .ri-item {
                        display: flex;
                        align-items: center;
                        margin-bottom: 10px;
                        .ritem-label {
                            width: 50px;
                            >span {
                                color: red;
                            }
                        }
                        .ritem-value {
                            flex: 1;
                        }
                    }
                }
            }
        }
    }
}

.rp-input {
    position: relative;
    .el-button {
        height: 100%;
    }
    .el-dropdown {
        position: absolute;
        right: 0;
        height: 100%;
    }
    >.el-button {
        position: absolute;
        right: 0;
        height: 100%;
    }
}

.tezan-wrap {
    height: 100%;
}

.copytopic-drawer-wrap {
    .el-drawer__body {
        padding: 0;
    }
}

.copytopic-drawer-inner-wrap {
    height: 100%;
}

.copy-topic-container {
    height: 100%;
    .cti-header {
        padding: 0 1rem;
        text-align: right;
    }
    .copy-topic-inner {
        height: calc(100% - 30px);
        overflow: auto;
        .single-copy {
            margin: 20px;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 0 5px #ddd;
            cursor: pointer;
            &:hover {
                box-shadow: 0 0 5px var(--el-color-primary-light-5);
            }
            .copy-txt {
                position: relative;
                flex: 1;
                .ct-img {
                    width: 30px;
                    position: absolute;
                    top: -30px;
                    left: -20px;
                    img {
                        width: 100%;
                        border-radius: 50%;
                    }
                    .avavter-chart {
                        background: #5e88d3;
                        text-align: center;
                        width: 30px;
                        height: 30px;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: #fff;
                    }
                }
            }
            .copy-del {
                width: 170px;
                text-align: right;
            }
        }
    }
}

.selection-tooltip {
    position: absolute;
    // width: 24px;
    // height: 24px;
    background-color: #409EFF;
    color: white;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    cursor: pointer;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.2s;
    padding: 3px;
    .ding {
        width: 15px;
        height: 15px;
        // background: url("../images/ding.svg") center/100% no-repeat;
    }
    &:hover {
        background-color: #66b1ff;
        transform: scale(1.1);
        &::after {
            opacity: 1;
        }
    }
    &::after {
        content: "添加所选到黑板";
        position: absolute;
        top: -25px;
        left: 50%;
        transform: translateX(-50%);
        background: #333;
        color: #fff;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        opacity: 0;
        transition: opacity 0.3s;
    }
}

.full-report {
    .chart-inner {
        min-height: 500px;
    }
    .slide-val {
        margin: 20px 0;
    }
    .readonly {
        // border-bottom: 2px solid var(--el-color-primary-light-3);
        margin-bottom: 30px;
        .chart-area {
            margin-top: 30px;
        }
    }
    .report-item {
        border-bottom: 2px dashed #ddd;
        margin-bottom: 2rem;
    }
    // height: 100%;
    // .report-item {
    //     min-height: 100%;
    //     .readonly {
    //         height: 100vh;
    //     }
    // }
}

.bst-start-inner {
    width: 100%;
    .inner-text-area {
        .ita-label {
            font-size: 0.9rem;
            color: var(--el-color-primary);
            margin-bottom: .3rem;
            display: block;
            font-weight: bold;
        }
    }
    .bst-textarea {
        margin: 0.5rem 0;
    }
    .bst-btns {
        margin-top: 20px;
        text-align: center;
    }
}

#excel-area-container {
    position: relative;
    .scroll-hint {
        position: sticky;
        bottom: 0;
        background: white;
        padding: 10px;
        border-top: 1px solid #eee;
        // position: absolute;
        // bottom: 15px;
        // left: 50%;
        // transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.5);
        color: white;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 1;
        transition: opacity 0.3s;
        z-index: 10;
        width: 100px;
        left: calc(50% - 50px);
    }
    &.at-bottom {
        .scroll-hint {
            opacity: 0;
        }
    }
}

.readonly {
    &.carousel-container {
        .carousel {
            flex-wrap: wrap;
            justify-content: center;
        }
    }
    .financial-statements-wrap .excel-area,
    &.market-segments-wrap .excel-area,
    .market-segments-wrap .excel-area {
        overflow: initial;
    }
}

// 管理端
.manage-index-wrap {
    height: 100vh;
    .main-content {
        height: calc(100% - 75px);
        overflow: auto;
    }
    .main-title {
        font-size: 1.3rem;
        line-height: 1.5;
        // margin-bottom: 20px;
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .mi-topbar {
        padding: 0 20px;
        height: 70px;
        background: var(--el-color-primary);
        display: flex;
        align-items: center;
        justify-content: space-between;
        .mit-logo {
            font-size: 1.8rem;
            // font-family: cursive;
            color: #fff;
        }
    }
    .mi-main {
        height: calc(100% - 70px);
        .splitter {
            display: flex;
            width: 100%;
            height: 100%;
            .splitter-side {
                width: 200px;
                height: 100%;
                .el-menu {
                    height: 100%;
                }
            }
            .splitter-content {
                flex: 1;
                overflow: hidden;
                &.full-page {
                    height: 100vh;
                }
                .main-wrap {
                    height: 100%;
                }
            }
        }
    }
    .mc-inner {
        display: flex;
        flex-wrap: wrap;
        // justify-content: space-between;
        gap: 3%;
        padding: 0 20px;
    }
    .mci-item {
        width: 31%;
        border: 1px solid #ddd;
        // padding: 15px;
        border-radius: 5px;
        box-shadow: 0 0 12px #ddd;
        overflow: hidden;
        margin-bottom: 3rem;
        cursor: pointer;
        display: flex;
        flex-direction: column;
        .mci-item-img {
            height: 280px;
            position: relative;
            flex-shrink: 0;
            >img {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }
        .mci-item-case-files {
            max-height: 90px;
            overflow: auto;
            .case-file-single {
                display: flex;
                align-items: center;
                // justify-content: space-between;
                padding: 3px 10px;
                .cf-name {
                    padding-right: 5px;
                    cursor: default;
                    text-decoration: underline;
                    display: flex;
                    align-items: center;
                    color: #777;
                    overflow: hidden;
                    font-size: 0.8rem;
                    flex: 1;
                    &.cf-name-success {
                        color: var(--el-color-success);
                    }
                    &.cf-name-error {
                        color: var(--el-color-danger);
                    }
                    &.cf-name-warning {
                        color: var(--el-color-warning);
                    }
                    .el-icon {
                        margin-right: 5px;
                        &.casefile-preview {
                            background: rgb(198, 226, 255);
                            color: rgb(51, 126, 204);
                            border-radius: 5px;
                            padding: 3px;
                            height: 1.2rem;
                            width: 1.2rem;
                            cursor: pointer;
                        }
                    }
                    .text {
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }
                }
                .cf-status {
                    max-width: 35%;
                    .el-tag {
                        width: 100%;
                        overflow: hidden;
                        .el-tag__content {
                            overflow: hidden;
                            text-overflow: ellipsis;
                            font-size: 0.7rem;
                        }
                    }
                }
            }
        }
        .mci-item-info {
            padding: 10px;
            display: flex;
            flex-direction: column;
            flex-grow: 1;
            .mciinfo-title {
                font-size: 1.2rem;
                font-weight: bold;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            .mciinfo-description {
                font-size: 0.9rem;
                color: #666;
                margin: 10px 0;
                flex-grow: 1;
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
        .mciinfo-opes {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .per-ope {
                display: flex;
                justify-items: center;
                .ope-item {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #eaeaea;
                    color: #514d4d;
                    border-radius: 5px;
                    padding: 3px 6px;
                    margin-right: 5px;
                    font-size: 0.8rem;
                    &:nth-last-of-type(1) {
                        margin-right: 0;
                    }
                    &.ope-preview {
                        background: rgb(198, 226, 255);
                        color: rgb(51, 126, 204);
                    }
                    &.ope-del {
                        background: var(--el-color-danger-light-7);
                        color: var(--el-color-danger-dark-2);
                    }
                    &.ope-active {
                        .el-icon {
                            margin-right: 5px;
                        }
                        &.isactive {
                            color: var(--el-color-primary);
                            background: var(--el-color-primary-light-7);
                        }
                    }
                }
            }
            .mci-state {
                color: var(--el-color-primary);
                background: var(--el-color-primary-light-9);
                .mci-innder-state {
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                    background: rgb(198, 226, 255);
                    color: rgb(51, 126, 204);
                    border-radius: 5px;
                    padding: 3px 6px;
                    margin-right: 5px;
                    font-size: 0.8rem;
                    >.el-icon {
                        margin-right: 5px;
                    }
                    &.isactive {
                        color: var(--el-color-success);
                        background: var(--el-color-success-light-9);
                    }
                }
                .el-tag {
                    .el-tag__content {
                        display: flex;
                        align-items: center;
                    }
                }
            }
        }
        .mci-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
            .mciid-ope {
                opacity: 0;
                cursor: pointer;
                &.mciid-ope-show {
                    opacity: 1;
                }
            }
            .mciid-title {
                font-weight: bold;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                display: flex;
                align-items: center;
                .doc-ready {
                    margin-right: 5px;
                }
            }
        }
        &:hover {
            box-shadow: 0 0 12px var(--el-color-primary-light-5);
            .mci-top {
                .mciid-ope {
                    opacity: 1;
                }
            }
        }
        &.doc-failed {
            box-shadow: none;
            background: #f5f5f5;
            cursor: not-allowed;
        }
        &.doc-loading {
            box-shadow: 0 0 12px var(--el-color-primary-light-5);
        }
        &.published {
            &:hover {
                box-shadow: 0 0 12px var(--el-color-success-light-5);
            }
        }
        &.active {
            background: var(--el-color-primary-light-9);
            &:hover {
                box-shadow: 0 0 12px var(--el-color-primary-light-5);
            }
        }
        .mcii-img {
            float: left;
            width: 210px;
            margin-right: 10px;
            >img {
                width: 100%;
                height: auto;
                display: block;
            }
        }
        .mciid-des {
            font-size: 0.8rem;
            color: #666;
        }
    }
}

.img-to-base64-picture-card {
    .preview-img {
        width: 120px;
        >img {
            width: 100%;
        }
    }
}

.img-upload-base64 {
    position: relative;
    .upload-icon {
        .el-icon {
            color: #6f6a6a;
            font-size: 2rem;
        }
    }
    .sub-hint-txt {
        font-size: 0.8rem;
        color: #666;
    }
    .sub-hint {
        border-radius: 5px;
        position: absolute;
        width: 148px;
        height: 148px;
        left: 0;
        top: 0;
        opacity: 0;
        cursor: default;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        background-color: var(--el-overlay-color-lighter);
        transition: opacity var(--el-transition-duration);
        &:hover {
            opacity: 1;
        }
        .el-icon {
            font-size: 1.5rem;
            color: #fff;
            cursor: pointer;
            +.el-icon {
                margin-left: 10px;
            }
            &:hover {
                color: var(--el-color-primary);
            }
        }
    }
}

.casedetail-drawer-wrap {
    .dia-ope {
        margin-top: 30px;
        text-align: center;
        .el-button {
            padding: 0 3rem;
        }
    }
}

$step-colors: (#FF9AA2,
#FFB7B2,
#FFDAC1,
#E2F0CB,
#B5EAD7,
#C7CEEA,
#F8B195,
#F67280,
#C06C84,
#6C5B7B,
#355C7D,
#A8E6CE,
#DCEDC2,
#FFEEAD,
#D4A5A5,
#92A8D1);
.showoff-index-wrap {
    overflow: hidden;
    background: linear-gradient(135deg, #1e5799 0%, #3a7bb8 20%, #5ba4d5 40%, #7db9e8 60%, #a8d0f5 80%, #c7e2ff 100%);
    color: #333;
    height: 100vh;
    #impress {
        min-height: 100vh;
        position: relative;
        .step-view {
            transition: opacity 1s;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            padding: 40px;
            width: 900px;
            height: 600px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            overflow: hidden;
            @for $i from 1 through length($step-colors) {
                &:nth-child(#{$i + 1}) {
                    /* +1 跳过全景视图步骤 */
                    background-color: nth($step-colors, $i);
                }
            }
            h1,
            h2 {
                margin-bottom: 30px;
                color: #333;
            }
            p,
            li {
                font-size: 24px;
                line-height: 1.6;
            }
            /* 新增动画效果 */
            .fade-in {
                animation: fadeIn 1.5s ease-in-out;
            }
            .slide-up {
                animation: slideUp 1.5s ease-out;
            }
            .zoom-in {
                animation: zoomIn 1.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            }
            .typewriter {
                animation: typewriter 2s steps(20) forwards;
                overflow: hidden;
                white-space: nowrap;
                border-right: 3px solid #333;
            }
            .flip-in {
                animation: flipInX 1.5s ease-in-out;
            }
            .flip-in-delay {
                animation: flipInX 1.5s ease-in-out 0.5s forwards;
                opacity: 0;
            }
            .bounce-in {
                animation: bounceIn 1.5s;
            }
            .bounce-in-delay {
                animation: bounceIn 1.5s 0.5s forwards;
                opacity: 0;
            }
            .particle-text {
                position: relative;
                z-index: 2;
            }
            .wave-text {
                animation: wave 2s ease-in-out infinite;
                position: relative;
                z-index: 2;
            }
            .pulse-circle {
                width: 100px;
                height: 100px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                position: absolute;
                animation: pulse 2s infinite;
            }
            .particles {
                position: absolute;
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;
                z-index: 1;
                &::before {
                    content: "";
                    position: absolute;
                    width: 5px;
                    height: 5px;
                    background: white;
                    border-radius: 50%;
                    animation: particles 5s infinite;
                }
                &::after {
                    content: "";
                    position: absolute;
                    width: 3px;
                    height: 3px;
                    background: white;
                    border-radius: 50%;
                    animation: particles 7s infinite 1s;
                }
            }
        }
    }
    /* 全景视图样式 */
    .impress-enabled {
        position: relative;
    }
    .impress-on-overview .step {
        opacity: 1;
        cursor: pointer;
    }
    /* 提示信息 */
    .hint {
        position: fixed;
        bottom: 20px;
        left: 0;
        right: 0;
        text-align: center;
        color: white;
        opacity: 0.7;
        font-size: 14px;
        z-index: 100;
        pointer-events: none;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }
    /* 关键帧动画 */
    @keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }
    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(50px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    @keyframes zoomIn {
        from {
            opacity: 0;
            transform: scale(0.5);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }
    @keyframes typewriter {
        from {
            width: 0;
        }
        to {
            width: 100%;
        }
    }
    @keyframes flipInX {
        from {
            opacity: 0;
            transform: perspective(400px) rotateX(90deg);
        }
        to {
            opacity: 1;
            transform: perspective(400px) rotateX(0);
        }
    }
    @keyframes bounceIn {
        0% {
            opacity: 0;
            transform: scale(0.3);
        }
        50% {
            opacity: 1;
            transform: scale(1.05);
        }
        70% {
            transform: scale(0.9);
        }
        100% {
            opacity: 1;
            transform: scale(1);
        }
    }
    @keyframes pulse {
        0% {
            transform: scale(0.8);
            opacity: 0.7;
        }
        50% {
            transform: scale(1.2);
            opacity: 0.3;
        }
        100% {
            transform: scale(0.8);
            opacity: 0.7;
        }
    }
    @keyframes wave {
        0%,
        100% {
            transform: translateY(0);
        }
        50% {
            transform: translateY(-10px);
        }
    }
    @keyframes particles {
        0% {
            transform: translate(0, 0);
            opacity: 0;
        }
        10% {
            opacity: 1;
        }
        90% {
            opacity: 1;
        }
        100% {
            transform: translate(random(200) - 100 + px, random(200) - 100 + px);
            opacity: 0;
        }
    }
}

.casedetail-drawer-inner-wrap {
    >.el-alert {
        margin-bottom: 1rem;
    }
}

.doc-ready {
    color: var(--el-color-success);
}

.passed-single {
    margin-bottom: 2rem;
    padding: 10px;
    border-radius: 5px;
    background: #efefef;
    >div {
        line-height: 1.5;
        font-size: 0.9rem;
    }
    .ps-name {
        font-size: 1rem;
        font-weight: bold;
    }
    .ps-text {
        margin: 10px 0;
    }
    .el-tag {
        margin-right: 5px;
        margin-bottom: 5px;
    }
}

.passed-themes-dialog {
    height: calc(100% - 30vh);
    .el-dialog__body {
        height: calc(100% - 40px);
        .passed-theme-wrap {
            height: 100%;
            overflow: auto;
        }
    }
}

.vc-title {
    .el-badge {
        margin-left: 20px;
    }
}

.preview-wrap {
    height: 100vh;
    position: relative;
    .change-step {
        position: absolute;
        top: calc(50% - 20px);
        width: 40px;
        height: 40px;
        background: var(--el-color-primary-light-5);
        border: var(--el-color-primary);
        border-radius: 50%;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        &:hover {
            background: var(--el-color-primary-light-3);
        }
        &.previous-step {
            left: 1rem;
        }
        &.next-step {
            right: 1rem;
        }
    }
}

// 课程预览
.course-view-wrap {
    height: 100%;
    .current-course {
        text-align: center;
        .cc-name {
            font-size: 1.2rem;
            font-weight: bold;
            padding-top: 2rem;
        }
        .cc-info {
            font-size: 0.8rem;
            line-height: 2;
            margin-top: 1rem;
            color: #666;
        }
    }
    .course-group-info-wrap {
        height: calc(100% - 110px);
        overflow: auto;
        display: flex;
        padding-top: 20px;
        // gap: 20px;
        .group-view-step {
            width: 300px;
            min-width: 300px;
            padding: 10px;
            border: 1px solid #ad0303;
            border-top: 5px solid #ad0303;
            margin-left: 20px;
            &:nth-last-of-type(1) {
                margin-right: 20px;
            }
            .gvs-header {
                // margin: -10px;
                .gvs-label {
                    font-size: 0.8rem;
                    line-height: 2;
                    font-weight: bold;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }
                .gvs-info {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    >div {
                        width: 45%;
                    }
                }
            }
        }
    }
}

.gvs-steps {
    height: calc(100% - 60px);
    overflow: auto;
    .per-step {
        $step-colors: #3B82F6, #10B981, #F59E0B, #8B5CF6, #09534b;
        @for $i from 1 through length($step-colors) {
            &:nth-child(#{length($step-colors)}n+#{$i}) {
                $color: nth($step-colors, $i);
                $color-rgb: red($color), green($color), blue($color);
                --step-color: #{$color};
                --step-light-color: rgba(#{$color-rgb},
                0.5);
                --step-dark-color: mix(#000,
                #{$color},
                20%);
                --step-border-color: rgba(#{$color-rgb},
                0.4);
            }
        }
        position: relative;
        padding: 1rem;
        background-color: #eceef0;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0,
        0,
        0,
        0.05);
        border: 1px solid #e2e8f0;
        border-left: 3px solid var(--step-color);
        margin: 2rem 0;
        // 主步骤标题样式
        &::before {
            // content: attr(data-step);
            position: absolute;
            top: -12px;
            left: 20px;
            background: white;
            padding: 0 10px;
            font-size: 12px;
            font-weight: bold;
            color: var(--step-color);
        }
        .step-step {
            position: relative;
            padding: 0.75rem 1rem 0.75rem 2rem; // 左侧留空间给序号
            margin-bottom: 0;
            background-color: var(--step-light-color);
            ;
            border-radius: 6px;
            font-size: 14px;
            color: #334155;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.07);
            border-left: 2px solid var(--step-light-color);
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
            // 只有当有多个子步骤时才显示连接线
            .report-link {
                color: #fff;
                .el-link__inner {}
            }
            &:not(:last-child) {
                margin-bottom: 1.5rem;
                // 柔和的曲线箭头连接
                // &::after {
                //     content: "";
                //     position: absolute;
                //     bottom: -10px;
                //     left: 20px;
                //     width: calc(100% - 40px);
                //     height: 10px;
                //     background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 20'%3E%3Cpath d='M0,0 C30,20 70,20 100,0' fill='none' stroke='%23cbd5e1' stroke-width='1.5' stroke-linecap='round'/%3E%3Cpath d='M95,5 L100,0 L95,-5' fill='none' stroke='%23cbd5e1' stroke-width='1.5' stroke-linecap='round'/%3E%3C/svg%3E") no-repeat center center;
                //     background-size: 100% 100%;
                // }
            }
            // 悬停效果
            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                // background-color: var(--step-dark-color);
            }
            // 子步骤序号标记
            &::before {
                content: attr(data-step);
                position: absolute;
                left: 0.5rem;
                top: 50%;
                transform: translateY(-50%);
                width: 18px;
                height: 18px;
                background-color: var(--step-color);
                color: white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 10px;
                font-weight: bold;
            }
            &.step-no-id {
                background-color: #fff;
            }
        }
        // 只有一个子步骤时不需要额外间距
        .step-step:only-child {
            margin-bottom: 0;
        }
    }
}

.cost-pricing-readonly {
    &.full-report {
        overflow: auto;
        .component-market-segment {
            height: initial;
        }
        .excel-area {
            height: initial;
            margin-bottom: 1rem;
        }
    }
}

.course-report-readonly {
    &.readonly {
        overflow: auto;
        .excel-area {
            height: initial;
            margin-bottom: 1rem;
        }
    }
}

.task-user-tag {
    margin-left: 3px;
}

.custom-loader {
    transform: rotateZ(45deg);
    perspective: 1000px;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    color: #fff;
    &:before,
    &:after {
        content: '';
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        width: inherit;
        height: inherit;
        border-radius: 50%;
        transform: rotateX(70deg);
        animation: 1s spin linear infinite;
        color: var(--el-color-primary);
    }
    &:after {
        transform: rotateY(70deg);
        animation-delay: .4s;
    }
}

@keyframes rotate {
    0% {
        transform: translate(-50%, -50%) rotateZ(0deg);
    }
    100% {
        transform: translate(-50%, -50%) rotateZ(360deg);
    }
}

@keyframes rotateccw {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
        transform: translate(-50%, -50%) rotate(-360deg);
    }
}

@keyframes spin {
    0%,
    100% {
        box-shadow: .2em 0px 0 0px currentcolor;
    }
    12% {
        box-shadow: .2em .2em 0 0 currentcolor;
    }
    25% {
        box-shadow: 0 .2em 0 0px currentcolor;
    }
    37% {
        box-shadow: -.2em .2em 0 0 currentcolor;
    }
    50% {
        box-shadow: -.2em 0 0 0 currentcolor;
    }
    62% {
        box-shadow: -.2em -.2em 0 0 currentcolor;
    }
    75% {
        box-shadow: 0px -.2em 0 0 currentcolor;
    }
    87% {
        box-shadow: .2em -.2em 0 0 currentcolor;
    }
}

.bstopic-drawer-outter-wrap {
    height: 100%;
    .ai-loadingt-index {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 80%;
    }
    .ai-img {
        width: 250px;
        height: 250px;
        // background: url('../images/aianimiation.gif') center/cover no-repeat;
        >img {
            width: 100%;
        }
    }
}

.full-case-list {
    .el-form-item__content {
        flex: 1;
        display: block;
        .case-files-list {
            .case-file-single {
                display: flex;
                align-items: center;
                // justify-content: space-between;
                .cf-name {
                    padding-right: 5px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    color: var(--el-color-primary);
                    cursor: default;
                    text-decoration: underline;
                    display: flex;
                    align-items: center;
                    &.cf-name-success {
                        color: var(--el-color-success);
                    }
                    &.cf-name-error {
                        color: var(--el-color-danger);
                    }
                    &.cf-name-warning {
                        color: var(--el-color-warning);
                    }
                    .el-icon {
                        margin-right: 5px;
                        &.casefile-preview {
                            background: rgb(198, 226, 255);
                            color: rgb(51, 126, 204);
                            border-radius: 5px;
                            padding: 3px;
                            height: 1.2rem;
                            width: 1.2rem;
                            cursor: pointer;
                        }
                    }
                    .text {
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }
                }
                .cf-operation {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-left: 10px;
                    .el-icon {
                        color: var(--el-color-danger);
                        cursor: pointer;
                    }
                }
            }
        }
    }
}

.icon-success {
    color: var(--el-color-success);
}

.icon-error {
    color: var(--el-color-danger);
}

.manage-front-index-wrap {
    height: 100%;
}

.table-area-wrap {
    margin-bottom: 2rem;
    .taw-header {
        font-size: 1.2rem;
        color: #606266;
        line-height: 2;
        padding: 0 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .value-key-inner-key {
        font-size: 0.8rem;
        font-weight: bold;
        color: #949494;
    }
}

.mes-input-form {
    .el-message-box__container {
        flex: 1;
        .el-message-box__message {
            width: 100%;
        }
    }
}

.iner-mes {
    width: 100%;
    .iner-mes-item {
        margin-bottom: 1rem;
        .s-label {
            font-weight: 500;
            font-size: 0.8rem;
            color: #333;
        }
        input {
            width: 100%;
            --el-input-inner-height: calc(var(--el-input-height, 32px) - 2px);
            --el-input-height: var(--el-component-size);
            position: relative;
            font-size: var(--el-font-size-base);
            display: inline-flex;
            line-height: var(--el-input-height);
            box-sizing: border-box;
            vertical-align: middle;
            display: inline-flex;
            flex-grow: 1;
            align-items: center;
            justify-content: center;
            padding: 1px 11px;
            background-color: var(--el-input-bg-color, var(--el-fill-color-blank));
            background-image: none;
            border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
            cursor: text;
            transition: var(--el-transition-box-shadow);
            transform: translateZ(0);
            box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
        }
    }
}

// 排课管理页面样式
.schedule-classes-wrap {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px;
    background: #f5f5f5;

    .sc-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 0 10px;

        .sc-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .sc-actions {
            display: flex;
            gap: 10px;
        }
    }

    .sc-calendar {
        flex: 1;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 30px;
            border-bottom: 1px solid #ebeef5;
            background: #fafafa;

            .calendar-nav {
                display: flex;
                align-items: center;
                gap: 20px;

                .current-month {
                    font-size: 18px;
                    font-weight: bold;
                    color: #333;
                    min-width: 120px;
                    text-align: center;
                }
            }

            .calendar-actions {
                display: flex;
                gap: 10px;
            }
        }

        .calendar-weekdays {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            background: #f8f9fa;
            border-bottom: 1px solid #ebeef5;

            .weekday {
                padding: 12px 8px;
                text-align: center;
                font-weight: 600;
                color: #666;
                font-size: 14px;
            }
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            grid-template-rows: repeat(6, 1fr);
            flex: 1;
            min-height: 600px;
            position: relative; // 为绝对定位的跨天课程提供定位上下文

            .calendar-day {
                border-right: 1px solid #ebeef5;
                border-bottom: 1px solid #ebeef5;
                padding: 8px;
                cursor: pointer;
                transition: background-color 0.2s;
                position: relative;
                min-height: 100px;
                display: flex;
                flex-direction: column;

                &:hover {
                    background-color: #f0f9ff;
                }

                &.is-today {
                    background-color: #e3f2fd;

                    .day-number {
                        background: var(--el-color-primary);
                        color: white;
                        border-radius: 50%;
                        width: 24px;
                        height: 24px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-weight: bold;
                    }
                }

                &.is-other-month {
                    background-color: #fafafa;
                    color: #ccc;

                    .day-number {
                        color: #ccc;
                    }
                }

                &.has-classes {
                    background-color: #fff7e6;
                }

                &:nth-child(7n) {
                    border-right: none;
                }

                .day-number {
                    font-size: 14px;
                    font-weight: 500;
                    margin-bottom: 4px;
                    align-self: flex-start;
                    z-index: 10; // 确保日期数字在课程条目之上
                    position: relative;
                }

                .day-classes {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    gap: 2px;
                    position: relative;

                    .class-item {
                        background: var(--el-color-primary);
                        color: white;
                        padding: 2px 6px;
                        border-radius: 3px;
                        font-size: 12px;
                        cursor: pointer;
                        transition: all 0.2s;
                        position: relative;
                        z-index: 2;

                        &:hover {
                            background: var(--el-color-primary-dark-2);
                            transform: translateY(-1px);
                            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                            z-index: 3;
                        }

                        // 单日课程保持原样
                        &:not(.class-span) {
                            border-radius: 3px;
                        }

                        .class-content {
                            display: flex;
                            flex-direction: column;
                            gap: 1px;

                            .class-name {
                                font-weight: 500;
                                white-space: nowrap;
                                overflow: hidden;
                                text-overflow: ellipsis;
                            }

                            .class-time {
                                font-size: 10px;
                                opacity: 0.9;
                            }
                        }
                    }
                }
            }

            // 跨天课程的绝对定位样式
            .span-class-item {
                position: absolute;
                background: var(--el-color-primary);
                color: white;
                padding: 2px 8px;
                font-size: 12px;
                cursor: pointer;
                transition: all 0.2s;
                z-index: 5;
                border-radius: 4px;
                height: 22px;
                display: flex;
                align-items: center;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);

                &:hover {
                    background: var(--el-color-primary-dark-2);
                    transform: translateY(-1px);
                    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
                    z-index: 6;
                }

                // 为不同的课程添加不同的颜色
                &:nth-child(odd) {
                    background: #67c23a; // 绿色
                    &:hover {
                        background: #529b2e;
                    }
                }

                &:nth-child(even) {
                    background: #e6a23c; // 橙色
                    &:hover {
                        background: #b88230;
                    }
                }

                .span-class-content {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    width: 100%;
                    overflow: hidden;

                    .span-class-name {
                        font-weight: 500;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        flex: 1;
                        font-size: 12px;
                    }

                    .span-class-time {
                        font-size: 10px;
                        opacity: 0.9;
                        white-space: nowrap;
                        background: rgba(255, 255, 255, 0.2);
                        padding: 1px 4px;
                        border-radius: 2px;
                    }
                }
            }
        }
    }

    // 课程弹窗样式
    .el-dialog {
        .group-management {
            .group-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
                padding-bottom: 10px;
                border-bottom: 1px solid #ebeef5;

                span {
                    font-weight: 600;
                    color: #333;
                }
            }

            .groups-list {
                max-height: 400px;
                overflow-y: auto;

                .group-item {
                    border: 1px solid #ebeef5;
                    border-radius: 6px;
                    padding: 15px;
                    margin-bottom: 15px;
                    background: #fafafa;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    .group-title {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 12px;
                        padding-bottom: 8px;
                        border-bottom: 1px solid #e4e7ed;

                        span {
                            font-weight: 600;
                            color: #333;
                        }
                    }

                    .group-users {
                        .users-header {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: 10px;

                            span {
                                font-size: 14px;
                                color: #666;
                            }
                        }

                        .users-list {
                            .user-item {
                                display: flex;
                                gap: 10px;
                                align-items: center;
                                margin-bottom: 8px;

                                &:last-child {
                                    margin-bottom: 0;
                                }

                                .el-input {
                                    flex: 1;
                                }

                                .el-button {
                                    flex-shrink: 0;
                                }
                            }
                        }
                    }
                }
            }
        }

        .dialog-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        // 查看模式样式
        &.view-mode {
            .el-form-item__label {
                color: #666;
            }

            .el-input.is-disabled .el-input__inner,
            .el-date-editor.is-disabled .el-input__inner {
                background-color: #f8f9fa;
                border-color: #e9ecef;
                color: #495057;
            }
        }
    }
}
