<template>
    <div class="schedule-classes-wrap" v-loading="loading">
        <!-- 页面头部 -->
        <div class="sc-header">
            <div class="sc-title">排课管理</div>
            <div class="sc-actions">
                <el-button type="primary" @click="showAddClassDialog">
                    <el-icon><Plus /></el-icon>
                    新增排课
                </el-button>
            </div>
        </div>

        <!-- 日历主体 -->
        <div class="sc-calendar">
            <!-- 日历头部导航 -->
            <div class="calendar-header">
                <div class="calendar-nav">
                    <el-button @click="previousMonth" :icon="ArrowLeft" circle />
                    <div class="current-month">{{ currentMonthText }}</div>
                    <el-button @click="nextMonth" :icon="ArrowRight" circle />
                </div>
                <div class="calendar-actions">
                    <el-button @click="goToToday" size="small">今天</el-button>
                </div>
            </div>

            <!-- 星期标题 -->
            <div class="calendar-weekdays">
                <div class="weekday" v-for="day in weekdays" :key="day">{{ day }}</div>
            </div>

            <!-- 日历网格 -->
            <div class="calendar-grid">
                <div
                    class="calendar-day"
                    v-for="day in calendarDays"
                    :key="day.dateKey"
                    :class="{
                        'is-today': day.isToday,
                        'is-other-month': day.isOtherMonth,
                        'has-classes': day.classes.length > 0
                    }"
                    @click="handleDayClick(day)"
                >
                    <div class="day-number">{{ day.day }}</div>
                    <div class="day-classes">
                        <!-- 课程条目 -->
                        <div
                            class="class-item"
                            v-for="classItem in day.classes"
                            :key="classItem.id"
                            :class="{
                                'class-span': classItem.isSpan,
                                'class-start': classItem.isStart,
                                'class-end': classItem.isEnd,
                                'class-middle': classItem.isMiddle
                            }"
                            @click.stop="handleClassClick(classItem)"
                            @contextmenu.prevent="handleClassRightClick($event, classItem)"
                        >
                            <div class="class-content">
                                <div class="class-name">{{ classItem.courseName }}</div>
                                <div class="class-time" v-if="classItem.isStart">
                                    {{ formatTime(classItem.startTime) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 新增/编辑课程弹窗 -->
        <el-dialog
            v-model="classDialogVisible"
            :title="isEditMode ? '编辑课程' : '新增课程'"
            width="600px"
            @close="resetClassForm"
        >
            <el-form
                ref="classFormRef"
                :model="classForm"
                :rules="classFormRules"
                label-width="120px"
            >
                <el-form-item label="课程名称" prop="courseName">
                    <el-input v-model="classForm.courseName" placeholder="请输入课程名称" />
                </el-form-item>

                <el-form-item label="任课老师" prop="teacherName">
                    <el-input v-model="classForm.teacherName" placeholder="请输入任课老师姓名" />
                </el-form-item>

                <el-form-item label="开始时间" prop="startTime">
                    <el-date-picker
                        v-model="classForm.startTime"
                        type="datetime"
                        placeholder="选择开始时间"
                        format="YYYY-MM-DD HH:mm"
                        value-format="x"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item label="结束时间" prop="endTime">
                    <el-date-picker
                        v-model="classForm.endTime"
                        type="datetime"
                        placeholder="选择结束时间"
                        format="YYYY-MM-DD HH:mm"
                        value-format="x"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item label="作业截止时间" prop="submitDeadline">
                    <el-date-picker
                        v-model="classForm.submitDeadline"
                        type="datetime"
                        placeholder="选择作业提交截止时间"
                        format="YYYY-MM-DD HH:mm"
                        value-format="x"
                        style="width: 100%"
                    />
                </el-form-item>

                <!-- 课程小组成员 -->
                <el-form-item label="课程小组">
                    <div class="group-management">
                        <div class="group-header">
                            <span>小组管理</span>
                            <el-button type="primary" size="small" @click="addGroup">
                                <el-icon><Plus /></el-icon>
                                添加小组
                            </el-button>
                        </div>

                        <div class="groups-list">
                            <div
                                class="group-item"
                                v-for="(group, groupIndex) in classForm.groupUsers"
                                :key="group.id || groupIndex"
                            >
                                <div class="group-title">
                                    <span>第{{ groupIndex + 1 }}组</span>
                                    <el-button
                                        type="danger"
                                        size="small"
                                        text
                                        @click="removeGroup(groupIndex)"
                                        v-if="classForm.groupUsers.length > 1"
                                    >
                                        删除小组
                                    </el-button>
                                </div>

                                <div class="group-users">
                                    <div class="users-header">
                                        <span>小组成员</span>
                                        <el-button
                                            type="primary"
                                            size="small"
                                            text
                                            @click="addUser(groupIndex)"
                                        >
                                            <el-icon><Plus /></el-icon>
                                            添加成员
                                        </el-button>
                                    </div>

                                    <div class="users-list">
                                        <div
                                            class="user-item"
                                            v-for="(user, userIndex) in group.users"
                                            :key="user.id || userIndex"
                                        >
                                            <el-input
                                                v-model="user.name"
                                                placeholder="请输入成员姓名"
                                                size="small"
                                            />
                                            <el-input
                                                v-model="user.openId"
                                                placeholder="请输入成员ID"
                                                size="small"
                                            />
                                            <el-button
                                                type="danger"
                                                size="small"
                                                text
                                                @click="removeUser(groupIndex, userIndex)"
                                                v-if="group.users.length > 1"
                                            >
                                                删除
                                            </el-button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-form-item>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="classDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="saveClass" :loading="saving">
                        {{ isEditMode ? '更新' : '保存' }}
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<script setup>
import { computed, ref, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, ArrowLeft, ArrowRight } from '@element-plus/icons-vue';
import moment from 'moment';

// 加载状态
const loading = ref(false);
const saving = ref(false);

// 当前日期和月份
const currentDate = ref(new Date());
const today = new Date();

// 星期标题
const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];

// 课程数据
const classes = ref([]);

// 弹窗相关
const classDialogVisible = ref(false);
const isEditMode = ref(false);
const classFormRef = ref();

// 课程表单
const classForm = ref({
    id: '',
    courseName: '',
    teacherName: '',
    teacher: '',
    startTime: '',
    endTime: '',
    submitDeadline: '',
    groupUsers: [{
        id: '',
        groupIndex: 0,
        users: [{
            id: '',
            name: '',
            openId: ''
        }]
    }]
});

// 表单验证规则
const classFormRules = {
    courseName: [
        { required: true, message: '请输入课程名称', trigger: 'blur' }
    ],
    teacherName: [
        { required: true, message: '请输入任课老师姓名', trigger: 'blur' }
    ],
    startTime: [
        { required: true, message: '请选择开始时间', trigger: 'change' }
    ],
    endTime: [
        { required: true, message: '请选择结束时间', trigger: 'change' },
        {
            validator: (rule, value, callback) => {
                if (value && classForm.value.startTime && value <= classForm.value.startTime) {
                    callback(new Error('结束时间必须晚于开始时间'));
                } else {
                    callback();
                }
            },
            trigger: 'change'
        }
    ],
    submitDeadline: [
        { required: true, message: '请选择作业提交截止时间', trigger: 'change' }
    ]
};

// 计算属性：当前月份文本
const currentMonthText = computed(() => {
    return moment(currentDate.value).format('YYYY年MM月');
});

// 计算属性：日历天数数组
const calendarDays = computed(() => {
    const year = currentDate.value.getFullYear();
    const month = currentDate.value.getMonth();

    // 获取当月第一天和最后一天
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);

    // 获取第一天是星期几（0=周日）
    const firstDayWeek = firstDay.getDay();

    // 获取上个月需要显示的天数
    const prevMonth = new Date(year, month - 1, 0);
    const prevMonthDays = prevMonth.getDate();

    const days = [];

    // 添加上个月的天数
    for (let i = firstDayWeek - 1; i >= 0; i--) {
        const day = prevMonthDays - i;
        const date = new Date(year, month - 1, day);
        days.push(createDayObject(date, day, true));
    }

    // 添加当月的天数
    for (let day = 1; day <= lastDay.getDate(); day++) {
        const date = new Date(year, month, day);
        days.push(createDayObject(date, day, false));
    }

    // 添加下个月的天数，补齐42个格子（6周）
    const remainingDays = 42 - days.length;
    for (let day = 1; day <= remainingDays; day++) {
        const date = new Date(year, month + 1, day);
        days.push(createDayObject(date, day, true));
    }

    return days;
});

// 创建日期对象
const createDayObject = (date, day, isOtherMonth) => {
    const dateKey = moment(date).format('YYYY-MM-DD');
    const isToday = moment(date).isSame(today, 'day');

    // 获取当天的课程
    const dayClasses = getClassesForDay(date);

    return {
        date,
        day,
        dateKey,
        isToday,
        isOtherMonth,
        classes: dayClasses
    };
};

// 获取指定日期的课程
const getClassesForDay = (date) => {
    const dayStart = moment(date).startOf('day');
    const dayEnd = moment(date).endOf('day');

    return classes.value.filter(classItem => {
        const startTime = moment(classItem.startTime);
        const endTime = moment(classItem.endTime);

        // 检查课程是否在这一天
        return startTime.isBefore(dayEnd) && endTime.isAfter(dayStart);
    }).map(classItem => {
        const startTime = moment(classItem.startTime);
        const endTime = moment(classItem.endTime);
        const currentDay = moment(date);

        // 判断跨天课程的显示状态
        const isStart = startTime.isSame(currentDay, 'day');
        const isEnd = endTime.isSame(currentDay, 'day');
        const isMiddle = !isStart && !isEnd;
        const isSpan = !startTime.isSame(endTime, 'day');

        return {
            ...classItem,
            isStart,
            isEnd,
            isMiddle,
            isSpan
        };
    });
};

// 方法：上一个月
const previousMonth = () => {
    const newDate = new Date(currentDate.value);
    newDate.setMonth(newDate.getMonth() - 1);
    currentDate.value = newDate;
};

// 方法：下一个月
const nextMonth = () => {
    const newDate = new Date(currentDate.value);
    newDate.setMonth(newDate.getMonth() + 1);
    currentDate.value = newDate;
};

// 方法：回到今天
const goToToday = () => {
    currentDate.value = new Date();
};

// 方法：处理日期点击
const handleDayClick = (day) => {
    if (day.isOtherMonth) {
        // 如果点击的是其他月份的日期，跳转到对应月份
        currentDate.value = new Date(day.date);
    }
    // 可以在这里添加其他日期点击逻辑
};

// 方法：处理课程点击
const handleClassClick = (classItem) => {
    // 检查是否可以编辑（未来的课程才能编辑）
    const now = new Date();
    const startTime = new Date(parseInt(classItem.startTime));

    if (startTime > now) {
        editClass(classItem);
    } else {
        ElMessage.info('已开始或已结束的课程无法编辑');
    }
};

// 方法：处理课程右键点击
const handleClassRightClick = (event, classItem) => {
    // 检查是否可以删除（未开始的课程才能删除）
    const now = new Date();
    const startTime = new Date(parseInt(classItem.startTime));

    if (startTime > now) {
        // 显示右键菜单或直接删除
        ElMessageBox.confirm(
            `确定要删除课程"${classItem.courseName}"吗？`,
            '确认删除',
            {
                confirmButtonText: '删除',
                cancelButtonText: '取消',
                type: 'warning'
            }
        ).then(() => {
            deleteClass(classItem);
        }).catch(() => {
            // 用户取消删除
        });
    } else {
        ElMessage.info('已开始或已结束的课程无法删除');
    }
};

// 方法：格式化时间显示
const formatTime = (timestamp) => {
    return moment(parseInt(timestamp)).format('HH:mm');
};
// 方法：显示新增课程弹窗
const showAddClassDialog = () => {
    isEditMode.value = false;
    resetClassForm();
    classDialogVisible.value = true;
};

// 方法：编辑课程
const editClass = (classItem) => {
    isEditMode.value = true;
    classForm.value = {
        id: classItem.id,
        courseName: classItem.courseName,
        teacherName: classItem.teacherName,
        teacher: classItem.teacher,
        startTime: classItem.startTime,
        endTime: classItem.endTime,
        submitDeadline: classItem.submitDeadline,
        groupUsers: JSON.parse(JSON.stringify(classItem.groupUsers || [{
            id: '',
            groupIndex: 0,
            users: [{
                id: '',
                name: '',
                openId: ''
            }]
        }]))
    };
    classDialogVisible.value = true;
};

// 方法：重置表单
const resetClassForm = () => {
    classForm.value = {
        id: '',
        courseName: '',
        teacherName: '',
        teacher: '',
        startTime: '',
        endTime: '',
        submitDeadline: '',
        groupUsers: [{
            id: '',
            groupIndex: 0,
            users: [{
                id: '',
                name: '',
                openId: ''
            }]
        }]
    };
    if (classFormRef.value) {
        classFormRef.value.clearValidate();
    }
};

// 方法：添加小组
const addGroup = () => {
    classForm.value.groupUsers.push({
        id: '',
        groupIndex: classForm.value.groupUsers.length,
        users: [{
            id: '',
            name: '',
            openId: ''
        }]
    });
};

// 方法：删除小组
const removeGroup = (groupIndex) => {
    if (classForm.value.groupUsers.length > 1) {
        classForm.value.groupUsers.splice(groupIndex, 1);
        // 重新设置 groupIndex
        classForm.value.groupUsers.forEach((group, index) => {
            group.groupIndex = index;
        });
    }
};

// 方法：添加用户
const addUser = (groupIndex) => {
    classForm.value.groupUsers[groupIndex].users.push({
        id: '',
        name: '',
        openId: ''
    });
};

// 方法：删除用户
const removeUser = (groupIndex, userIndex) => {
    if (classForm.value.groupUsers[groupIndex].users.length > 1) {
        classForm.value.groupUsers[groupIndex].users.splice(userIndex, 1);
    }
};

// 方法：保存课程
const saveClass = async () => {
    if (!classFormRef.value) return;

    try {
        const valid = await classFormRef.value.validate();
        if (!valid) return;

        saving.value = true;

        // 准备数据
        const classData = {
            ...classForm.value,
            teacher: classForm.value.teacherName, // 设置 teacher 字段
            startTime: parseInt(classForm.value.startTime),
            endTime: parseInt(classForm.value.endTime),
            submitDeadline: parseInt(classForm.value.submitDeadline)
        };

        if (isEditMode.value) {
            // 编辑课程 - 伪代码
            await updateClassInfo(classData);
            ElMessage.success('课程更新成功');

            // 更新本地数据
            const index = classes.value.findIndex(item => item.id === classData.id);
            if (index !== -1) {
                classes.value[index] = classData;
            }
        } else {
            // 新增课程 - 伪代码
            const result = await createClassInfo(classData);
            ElMessage.success('课程创建成功');

            // 添加到本地数据
            classes.value.push({
                ...classData,
                id: result.id || generateId()
            });
        }

        classDialogVisible.value = false;
        resetClassForm();

    } catch (error) {
        console.error('保存课程失败:', error);
        ElMessage.error('保存失败，请重试');
    } finally {
        saving.value = false;
    }
};

// 方法：删除课程
const deleteClass = async (classItem) => {
    try {
        await ElMessageBox.confirm(
            `确定要删除课程"${classItem.courseName}"吗？`,
            '确认删除',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        );

        loading.value = true;

        // 删除课程 - 伪代码
        await deleteClassInfo({ id: classItem.id });

        // 从本地数据中移除
        const index = classes.value.findIndex(item => item.id === classItem.id);
        if (index !== -1) {
            classes.value.splice(index, 1);
        }

        ElMessage.success('课程删除成功');

    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除课程失败:', error);
            ElMessage.error('删除失败，请重试');
        }
    } finally {
        loading.value = false;
    }
};
// ==================== API 伪代码 ====================

// 获取课程列表
const fetchClassList = async () => {
    try {
        loading.value = true;

        // 伪代码：调用 GraphQL 接口获取课程列表
        // const result = await apolloClient.query({
        //     query: gql`
        //         query getClassList {
        //             classList {
        //                 id
        //                 courseName
        //                 teacherName
        //                 teacher
        //                 startTime
        //                 endTime
        //                 submitDeadline
        //                 groupUsers {
        //                     id
        //                     groupIndex
        //                     users {
        //                         id
        //                         name
        //                         openId
        //                     }
        //                 }
        //             }
        //         }
        //     `
        // });

        // 模拟数据
        const mockData = [
            {
                id: '1',
                courseName: '价格策略分析',
                teacherName: '张教授',
                teacher: 'teacher001',
                startTime: Date.now() + 86400000, // 明天
                endTime: Date.now() + 86400000 + 7200000, // 明天+2小时
                submitDeadline: Date.now() + 172800000, // 后天
                groupUsers: [
                    {
                        id: 'group1',
                        groupIndex: 0,
                        users: [
                            { id: 'user1', name: '学生A', openId: 'openid1' },
                            { id: 'user2', name: '学生B', openId: 'openid2' }
                        ]
                    }
                ]
            },
            {
                id: '2',
                courseName: '市场竞争分析',
                teacherName: '李教授',
                teacher: 'teacher002',
                startTime: Date.now() + 259200000, // 3天后
                endTime: Date.now() + 345600000, // 4天后
                submitDeadline: Date.now() + 432000000, // 5天后
                groupUsers: [
                    {
                        id: 'group2',
                        groupIndex: 0,
                        users: [
                            { id: 'user3', name: '学生C', openId: 'openid3' }
                        ]
                    }
                ]
            }
        ];

        classes.value = mockData;

    } catch (error) {
        console.error('获取课程列表失败:', error);
        ElMessage.error('获取课程列表失败');
    } finally {
        loading.value = false;
    }
};

// 创建课程
const createClassInfo = async (classData) => {
    // 伪代码：调用 GraphQL 接口创建课程
    // return await apolloClient.mutate({
    //     mutation: gql`
    //         mutation createClass($input: ClassInfoInput!) {
    //             createClass(input: $input) {
    //                 id
    //                 courseName
    //                 teacherName
    //                 teacher
    //                 startTime
    //                 endTime
    //                 submitDeadline
    //                 groupUsers {
    //                     id
    //                     groupIndex
    //                     users {
    //                         id
    //                         name
    //                         openId
    //                     }
    //                 }
    //             }
    //         }
    //     `,
    //     variables: { input: classData }
    // });

    // 模拟返回
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({ id: generateId() });
        }, 1000);
    });
};

// 更新课程
const updateClassInfo = async (classData) => {
    // 伪代码：调用 GraphQL 接口更新课程
    // return await apolloClient.mutate({
    //     mutation: gql`
    //         mutation updateClass($input: ClassInfoInput!) {
    //             updateClass(input: $input) {
    //                 id
    //                 courseName
    //                 teacherName
    //                 teacher
    //                 startTime
    //                 endTime
    //                 submitDeadline
    //                 groupUsers {
    //                     id
    //                     groupIndex
    //                     users {
    //                         id
    //                         name
    //                         openId
    //                     }
    //                 }
    //             }
    //         }
    //     `,
    //     variables: { input: classData }
    // });

    // 模拟返回
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({ success: true });
        }, 1000);
    });
};

// 删除课程
const deleteClassInfo = async (params) => {
    // 伪代码：调用 GraphQL 接口删除课程
    // return await apolloClient.mutate({
    //     mutation: gql`
    //         mutation deleteClass($id: String!) {
    //             deleteClass(id: $id) {
    //                 success
    //             }
    //         }
    //     `,
    //     variables: params
    // });

    // 模拟返回
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({ success: true });
        }, 1000);
    });
};

// 生成ID的工具函数
const generateId = () => {
    return 'class_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
};

// 生命周期
onMounted(() => {
    fetchClassList();
});
</script>