<template>
<div class="financial-statements-wrap" v-loading="loading">
    <div class="comparison-header">
        <div class="version-change">
            <div class="toget-report">
                <el-button type="primary" v-if="!(financialreports||economicreports)" @click="toGetStatement('财务视角和经济学视角')">分析财务数据</el-button>
            </div>
            <el-select v-model="currentversion" @change="changeTabView"  v-if="false&&viewitems.length>1">
            <el-option
                v-for="(item,index) in viewitems"
                :key="item.id"
                :label="versionLabel(item,index)"
                :value="item.id">
            </el-option>
            </el-select>
        </div>
    </div>
    <div class="content-inner-wrap">
        <div class="iframe-inner-wrap"
        ref="leftPanel"
        :style="{ width: leftWidth + '%' }">
            <iframe 
            :src="iframeurl"
            width="100%" 
            height="100%"
            ref="inneriframe"
            @load="iframeLoaded"
            frameborder="0"></iframe>
        </div>
        <div 
        class="drag-bar" 
        @mousedown="startDrag"
        v-if="financialreports||economicreports"
        ></div>
        <div class="excel-area"
        :style="{ width: (100 - leftWidth) + '%' }"
         v-if="financialreports||economicreports">
            <div class="excel-total-info">
                <div class="ana-types-change">
                    <el-select v-model="currentview" @change="countInitInfo">
                        <el-option
                            label="财务分析报表"
                            :disabled="!financialreports"
                            value="financial">
                        </el-option>
                        <el-option
                            label="经济分析报表"
                            :disabled="!economicreports"
                            value="economic">
                        </el-option>
                    </el-select>
                </div>
            </div>
            <!-- fcresults,vcresults,gmrresults -->
            <div class="table-area-wrap" >
                <div class="taw-header">
                    <div>FC 数据</div>
                    <div><el-button type="primary" size="small" @click="preAddData({first:'fixed_costs',second:-1})">添加数据</el-button></div>
                </div>
                <div class="row-header">
                    <div class="header-item"
                    v-for="(item,index) in rowheaders"
                    :class="item.value"
                    :key="item+index">{{item.label?item.label:item.value}}</div>
                    <div class="header-item header-item-operate-aaa">操作</div>
                </div>
                <div class="row-content">
                    <div class="content-single-row"
                    v-for="(item,index) in totaldatas?.fcresults"
                    :key="'itemfcresults'+index">
                    <template v-for="(iitem,iikey) in item" key="templater">
                        <div v-if="rowheaders.find((ritem)=>ritem.value===iikey)"
                            class="row-column column-cell "
                            :class="'row-column-'+iikey+index">
                            <div class="row-column-consumer_profile-inner"
                            @click="editCell(iitem,{first:'fixed_costs',second:index,third:iikey})"
                             v-if="iikey!=='values'">
                                <div class="column-label">
                                    <span >{{iitem}}</span>
                                </div>
                            </div>
                            <div class="row-column-consumer_profile-inner"
                             v-else>
                                <template v-if="iitem">
                                    <div class="column-label"
                                    v-for="(vitem,vindex) in iitem"
                                    :key="'value-key-'+vindex"
                                    >
                                        <div class="column-label-innder"
                                            v-for="(viivalue,viikey) in vitem"
                                            @click="editValueCell({vvalue:viivalue,vkey:viikey},{first:'fixed_costs',second:index,third:iikey,fourth:vindex})"
                                            :key="'value-key-inner-'+viikey">
                                            <span class="value-key-inner-key">{{viikey}}:</span>
                                            <span class="value-key-inner-value" @click.stop="editCell(viivalue,{first:'fixed_costs',second:index,third:iikey,fourth:vindex,fifth:viikey})">{{viivalue}}</span>
                                            <span class="value-key-inner-del"  @click.stop="preDelData({first:'fixed_costs',second:index,third:'values',fourth:vindex})">
                                                <el-icon><RemoveFilled /></el-icon>
                                            </span>
                                            
                                        </div>
                                    </div>
                                    <div class="column-label values-add" @click="editValueCell({vvalue:'',vkey:''},{first:'fixed_costs',second:index,third:iikey})">增加</div>
                                </template>
                                <template v-else>
                                    <div class="column-label  values-add" @click="editValueCell({vvalue:'',vkey:''},{first:'fixed_costs',second:index,third:iikey})">增加</div>
                                </template>
                            </div>
                        </div>
                    </template>
                    <div class="row-column row-edit row-edit-aaa">
                        <el-icon @click="preAddData({first:'fixed_costs',second:index})"><CirclePlusFilled /></el-icon>
                        <el-icon @click="preDelData({first:'fixed_costs',second:index})"><RemoveFilled /></el-icon>
                    </div>
                    </div>
                </div>
            </div>
            <div class="table-area-wrap">
                <div class="taw-header">
                    <div>VC 数据</div>
                    <div><el-button type="primary" size="small"  @click="preAddData({first:'variable_costs',second:-1})">添加数据</el-button></div>
                </div>
                <div class="row-header">
                    <div class="header-item"
                    v-for="(item,index) in rowheaders"
                    :class="item.value"
                    :key="item+index">{{item.label?item.label:item.value}}</div>
                    <div class="header-item header-item-operate-aaa">操作</div>
                </div>
                <div class="row-content">
                    <div class="content-single-row"
                    v-for="(item,index) in totaldatas?.vcresults"
                    :key="'item'+index">
                    <template v-for="(iitem,iikey) in item" key="templater">
                        <div v-if="rowheaders.find((ritem)=>ritem.value===iikey)"
                            class="row-column column-cell "
                            :class="'row-column-'+iikey+index">
                            <div class="row-column-consumer_profile-inner"
                            @click="editCell(iitem,{first:'variable_costs',second:index,third:iikey})"
                             v-if="iikey!=='values'">
                                <div class="column-label">
                                    <span>{{iitem}}</span>
                                </div>
                            </div>
                            <div class="row-column-consumer_profile-inner" v-else>
                                <template v-if="iitem">
                                    <div class="column-label"
                                        v-for="(vitem,vindex) in iitem"
                                        :key="'value-key-'+vindex">
                                        <div class="column-label-innder"
                                            v-for="(viivalue,viikey) in vitem"
                                            @click="editValueCell({vvalue:viivalue,vkey:viikey},{first:'variable_costs',second:index,third:iikey,fourth:vindex})"
                                            :key="'value-key-inner-'+viikey">
                                                <span class="value-key-inner-key">{{viikey}}:</span>
                                                <span class="value-key-inner-value" @click.stop="editCell(viivalue,{first:'variable_costs',second:index,third:iikey,fourth:vindex,fifth:viikey})">{{viivalue}}</span>
                                                <span class="value-key-inner-del"  @click.stop="preDelData({first:'variable_costs',second:index,third:'values',fourth:vindex})">
                                                    <el-icon><RemoveFilled /></el-icon>
                                                </span>
                                            
                                            </div>
                                        </div>
                                        <div class="column-label values-add" @click="editValueCell({vvalue:'',vkey:''},{first:'variable_costs',second:index,third:iikey})">增加</div>
                                </template>
                                <template v-else>
                                    <div class="column-label values-add" @click="editValueCell({vvalue:'',vkey:''},{first:'variable_costs',second:index,third:iikey})">增加</div>
                                </template>
                            </div>
                        </div>
                    </template>
                    <div class="row-column row-edit row-edit-aaa">
                        <el-icon @click="preAddData({first:'variable_costs',second:index})"><CirclePlusFilled /></el-icon>
                        <el-icon  @click="preDelData({first:'variable_costs',second:index})"><RemoveFilled /></el-icon>
                    </div>
                    </div>
                </div>
            </div>
            <div class="table-area-wrap">
                <div class="taw-header">
                        <div>毛利率数据</div>
                        <div><el-button type="primary" size="small"  @click="preAddData({first:'gross_margin_rate',second:''})">添加数据</el-button></div>
                </div>
                <div class="row-header">
                    <div class="header-item">维度</div>
                    <div class="header-item">利率</div>
                    <div class="header-item header-item-operate-aaa">操作</div>
                </div>
                <div class="row-content">
                    <div class="content-single-row"
                    v-for="(value,key) in totaldatas?.gmrresults"
                    :key="'itemgmrresults'+key">
                    <div class="row-column column-cell " :class="'row-column-'+key">
                        <div class="edit-able">
                            <span>{{key}}</span>
                            </div>
                    </div>
                    <div class="row-column column-cell " :class="'row-column-'+value">
                        <div class="edit-able" @click="editCell(value, {first:'gross_margin_rate',second:key})">
                            <span >{{value?value:"无"}}</span>
                        </div>
                    </div>
                    <div class="row-column row-edit row-edit-aaa">
                        <el-icon @click="preAddData({first:'gross_margin_rate',second:key})"><CirclePlusFilled /></el-icon>
                        <el-icon  @click="preDelData({first:'gross_margin_rate',second:key})"><RemoveFilled /></el-icon>
                    </div>
                    </div>
                </div>
            </div>
            <div class="table-area-wrap">
                <div class="taw-header">
                    <div>销量</div>
                    <div><el-button type="primary" size="small"  @click="preAddData({first:'volume',second:''})">添加数据</el-button></div>
                </div>
                <div class="row-header">
                    <div class="header-item">维度</div>
                    <div class="header-item">销量</div>
                    <div class="header-item header-item-operate-aaa">操作</div>
                </div>
                <div class="row-content">
                    <div class="content-single-row"
                    v-for="(value,key) in totaldatas?.volumeresults"
                    :key="'itemvolumeresults'+key">
                        <div class="row-column column-cell " :class="'row-column-'+key">
                            <div class="edit-able">
                                <span>{{key}}</span>
                                </div>
                        </div>
                        <div class="row-column column-cell " :class="'row-column-'+value">
                            <div class="edit-able"  @click="editCell(value, {first:'volume',second:key})">
                                <span >{{value?value:"无"}}</span>
                                </div>
                        </div>
                        <div class="row-column row-edit row-edit-aaa">
                            <el-icon @click="preAddData({first:'volume',second:key})"><CirclePlusFilled /></el-icon>
                            <el-icon  @click="preDelData({first:'volume',second:key})"><RemoveFilled /></el-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref,inject,onMounted, watch,nextTick } from 'vue';
import {getPriceViewInfo,updatePriceSessionView} from '@/assets/api/api.js'
import {getHashParams,versionLabel,debounceCustom,updateUrlHash} from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router';
import moment from 'moment'
const route = useRoute();
// 同名tab的不同版本
let viewitems=computed(()=>{
  let list=[]
  if(sessionStorage.getItem('currenttabviews')){
    try{
      list=JSON.parse(sessionStorage.getItem('currenttabviews'))
    }catch(err){
      list=[]
    }
  }
  return list
})
let viewid=computed(()=>{
  return sessionStorage.getItem('currenttabid')
})
let currentversion=ref('')
watch(viewid.value,()=>{
  currentversion.value=viewid.value
},{immediate:true})
watch(() => route.hash, () => {
    getData()
});

// 编辑相关状态
const editingCell = ref(null);
const inputRef = ref(null);
let hasviewid = getHashParams('viewid')
let baseData=ref({
    "excelPreviewUrl": "https://cloud.ketanyun.cn/preview-api/v3/index?url=https://cloud.ketanyun.cn/file/3dcc5f4d-03a7-4565-8ee7-68847e731d88",
    "financial":
    [
        {
        //     "variable_costs":
        //     [
        //         {
        //             "cost_type": "unit_variable_cost",
        //             "values":[{
        //             	"自产": 0.5,
        //             	"外包生产": 1.2
        //             }],
        //             "name": "化学试剂"
        //         },
        //          {
        //             "cost_type": "unit_variable_cost",
        //             "values":[{
        //                 "自产": 0.5,
        //                 "外包生产": 1.2
        //             },{
        //                 "自产": 0.5,
        //             }],
        //             "name": "化学试剂1"
        //         },
        //          {
        //             "cost_type": "unit_variable_cost",
        //             "values":[{
        //                 "自产": 0.5,
        //                 "外包生产": 1.2
        //             }],
        //             "name": "化学试剂2"
        //         },
        //          {
        //             "cost_type": "unit_variable_cost",
        //             "values":[{
        //                 "自产": 0.5,
        //                 "外包生产": 1.2
        //             }],
        //             "name": "化学试剂3"
        //         },
        //          {
        //             "cost_type": "unit_variable_cost",
        //             "values":[{
        //                 "自产": 0.5,
        //                 "外包生产": 1.2
        //             }],
        //             "name": "化学试剂4"
        //         },
        //          {
        //             "cost_type": "unit_variable_cost",
        //             "value":[{
        //                 "自产": 0.5,
        //                 "外包生产": 1.2
        //             }],
        //             "name": "化学试剂5"
        //         },
                
        //     ],
        //     "fixed_costs":[],
        //     "gross_margin_rate":
        //     {
        //         "annual_2025": 0.7,
        //         "annual_2027": 0.8496,
        //         "annual_2026": 0.7214,
        //         "annual_2029": 0.8784,
        //         "annual_2028": 0.8701
        //     },
            
        }
    ],
    "economic":[
        {
        //     "volume":
        //     {
        //         "annual_2024": 12113,
        //         "monthly_2024_12": 967
        //     },
        //     "variable_costs":
        //     [
        //         {
                    
        //             "cost_type": "total_variable_cost",
        //             "value":[
        //             	{"annual_2024": 1975506.576},
        //             	{"monthly_2024_12": 158427.624}
        //             ],
        //             "name": "食品Food1"
        //         },
        //         {
                    
        //             "cost_type": "total_variable_cost",
        //             "value":[
        //                 {"annual_2024": 1975506.576},
        //                 {"monthly_2024_12": 158427.624}
        //             ],
        //             "name": "食品Food2"
        //         },
        //         {
                    
        //             "cost_type": "total_variable_cost",
        //             "value":[
        //                 {"annual_2024": 1975506.576},
        //                 {"monthly_2024_12": 158427.624}
        //             ],
        //             "name": "食品Food3"
        //         },
        //         {
                    
        //             "cost_type": "total_variable_cost",
        //             "value":[
        //                 {"annual_2024": 1975506.576},
        //                 {"monthly_2024_12": 158427.624}
        //             ],
        //             "name": "食品Food4"
        //         },
        //         {
                    
        //             "cost_type": "total_variable_cost",
        //             "value":[
        //                 {"annual_2024": 1975506.576},
        //                 {"monthly_2024_12": 158427.624}
        //             ],
        //             "name": "食品Food5"
        //         },
        //         {
                    
        //             "cost_type": "total_variable_cost",
        //             "value":[
        //                 {"annual_2024": 1975506.576},
        //                 {"monthly_2024_12": 158427.624}
        //             ],
        //             "name": "食品Food6"
        //         },
               
        //     ],
        //     "fixed_costs":
        //     [
        //         {
        //             "values":[
        //                 {"annual_2024": 52797.516},
        //                 {"monthly_2024_12": 3252.48}
        //             ],
        //             "name": "后勤及人事部费用Administration & General"
        //         },
        //         {
        //             "values":[
        //                 {"annual_2024": 52797.516},
        //                 {"monthly_2024_12": 3252.48}
        //             ],
        //             "name": "后勤及人事部费用Administration & General"
        //         },
        //         {
        //             "values":[
        //                 {"annual_2024": 52797.516},
        //                 {"monthly_2024_12": 3252.48}
        //             ],
        //             "name": "后勤及人事部费用Administration & General"
        //         },
        //         {
        //             "values":[
        //                 {"annual_2024": 52797.516},
        //                 {"monthly_2024_12": 3252.48}
        //             ],
        //             "name": "后勤及人事部费用Administration & General"
        //         },
        //         {
        //             "values":[
        //                 {"annual_2024": 52797.516},
        //                 {"monthly_2024_12": 3252.48}
        //             ],
        //             "name": "后勤及人事部费用Administration & General"
        //         },
        //         {
        //             "values":[
        //                 {"annual_2024": 52797.516},
        //                 {"monthly_2024_12": 3252.48}
        //             ],
        //             "name": "后勤及人事部费用Administration & General"
        //         },

                
        //     ]
        }
        ]
})
let rowheaders=ref([]);
let rowdatas=ref([]);
let totaldatas=ref({});
let rowcolumns=ref([]);
let currentview=ref("financial")
let financialreports=ref(false)
let economicreports=ref(false)


// 
let loading =ref(false)
let dataid =ref('');
let textval =ref('');
const getData=(ischange)=>{
  textval.value="";
  dataid.value='';
  baseData.value={};
    if(ischange=='ischange'&&currentversion.value){
      toGetViewData(currentversion.value)
    }else if(hasviewid){
      toGetViewData(hasviewid)
    }
};
// 
const toGetViewData=(viewId)=>{
  loading.value=true;
  getPriceViewInfo({viewId}).then((res)=>{
    loading.value=false;
      dataid.value=res?.data?.priceSessionVIew?.id
        if(res?.data?.priceSessionVIew?.data){
            let chartdatas=[]
            try{
                chartdatas=JSON.parse(res.data.priceSessionVIew.data)
            }catch (err){
                ElMessage.warning("数据解析出错")
            }
            if(chartdatas){
                console.log(chartdatas)
                if(!chartdatas?.financial){
                    chartdatas.financial=[{}]
                }
                if(!chartdatas?.economic){
                    chartdatas.economic=[{}]
                }
              baseData.value=chartdatas
              countInitInfo()
            }else{
                ElMessage.warning("数据解析失败")
            }
        }
    })
    // .catch((err)=>{
    //   loading.value=false;
    //     ElMessage.warning("获取内容失败"+err)
    // });
};
let iframeurl =ref('');
// let financialrowdata=ref([]);
// let economicrowdata=ref([]);
let annualvc=ref(0);
let monthlyvc=ref(0);
let annualfc=ref(0);
let monthlyfc=ref(0);
let ablechange=ref(false);
// 默认左侧宽度比例
const leftWidth = ref(40);
const leftPanel = ref(null);
let isDragging = false;
let startX = 0;
let startWidth = 0;

const processFinancialTypeDatas=(reportitem)=>{
    let {fixed_costs,variable_costs,gross_margin_rate,volume}=reportitem;
    let fcresults=[];
    let vcresults=[];
    let gmrresults=gross_margin_rate;//{key:value}
    let volumeresults=volume;//{key:value}
    
    fixed_costs?.forEach((item)=>{
        let newitem ={
            name:item.name,
            values:item.values,
        }
        fcresults.push(newitem);
    })
    variable_costs?.forEach((item)=>{
        let newitem ={
            name:item.name,
            values:item.values,
        }
        vcresults.push(newitem);
    })

    return {fcresults,vcresults,gmrresults,volumeresults}
};
const processeEconomicTypeDatas=(reportitem)=>{
    let {fixed_costs,variable_costs,volume,gross_margin_rate}=reportitem;
    let fcresults=[];
    let vcresults=[];
    let volumeresults=volume;//{key:value}
    let gmrresults=gross_margin_rate;//{key:value}
    fixed_costs?.forEach((item)=>{
        let newitem ={
            name:item.name,
            values:item.values,
        }
        fcresults.push(newitem);
    })
    variable_costs?.forEach((item)=>{
        let newitem ={
            name:item.name,
            values:item.values,
        }
        vcresults.push(newitem);
    })

    return {fcresults,vcresults,gmrresults,volumeresults}
};
function reverseProcessTypeDatas(processedList) {
    // 初始化结果对象
    const reportitem = {
        fixed_costs: [],  
        variable_costs: [] 
    };
    // 分离固定成本和可变成本
    processedList.forEach(item => {
        const costItem = {
            name: item.name,
            monthly_amount: item.monthly_amount,
            annual_amount: item.annual_amount
        };

        if (item.type === 'FC') {
            reportitem.fixed_costs.push(costItem);
        } else if (item.type === 'VC') {
            reportitem.variable_costs.push(costItem);
        }
    });
    return reportitem;
}
const countInitInfo=()=>{
    rowheaders.value=[
        {
            value:"name",
            label:"名称"
        },
        {
            value:"values",
            label:"值"
        },
        // {
        //     value:"cost_type",
        //     label:"cost_type"
        // },
    ]
    let {excelPreviewUrl,financial,economic}=JSON.parse(JSON.stringify(baseData.value));
    // 
    iframeurl.value=excelPreviewUrl;
    // 下拉选项
    if(financial?.length&&economic?.length){
        financialreports.value=true;
        economicreports.value=true;
    }else if(financial?.length){
        currentview.value='financial'
        financialreports.value=true;
        economicreports.value=false;
        baseData.value.economic=[{}]
    }else if(economic?.length){
        currentview.value='economic';
        financialreports.value=false;
        economicreports.value=true;
        baseData.value.financial=[{}]
    }
    // 如果没有报告信息则直接展示全部iframe信息
    if(!(financialreports.value||economicreports.value)){
        leftWidth.value=100
    }else {
        leftWidth.value=40
    }
    // 
    if(financial||economic){
        if(currentview.value==='financial'&&financial[0]){
            totaldatas.value=processFinancialTypeDatas(financial[0]);
        }else if(currentview.value==='economic'&&economic[0]){
            totaldatas.value=processeEconomicTypeDatas(economic[0])
        }
    }
    console.log(totaldatas.value)
}
const changeTabView=()=>{
    getData('ischange')
}
const iframeLoaded=()=>{
    console.log('iframe loaded')
}


// countInitInfo()//测试
getData();
// !editingCell || editingCell.type !== 'fixed_costs'|| editingCell.index !== index || editingCell.key !== iikey
const editCell = (value,path) => {
    let {first,second,third,fourth,fifth}=path
    ElMessageBox.prompt(`修改内容:`, '编辑', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: value,
    // inputType: 'textarea',
    inputValidator: (value) => {
      if (!value || value.trim() === '') {
        return '内容不能为空'; 
      }
      return true; 
    },
    inputErrorMessage: '请输入有效内容',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        // if(sixth){//values[index].key-value
        //     totaldatas.value[first][second][third][fourth][fifth][sixth]=instance.inputValue;
        // }else 
        if(fifth){//values[index].key
            baseData.value[currentview.value][0][first][second][third][fourth][fifth]=instance.inputValue
        }else if(fourth){//values[index]
            // baseData.value[currentview.value][0][first][second][third][fourth]=JSON.parse(instance.inputValue)
        }else if(third){//name 等简单数据
           baseData.value[currentview.value][0][first][second][third]=instance.inputValue
        }else if(second){//volume,gross_margin_rate
            baseData.value[currentview.value][0][first][second]=instance.inputValue
        }
        console.log(baseData.value)
        // 页面展示修改
        countInitInfo()
        // 直接保存
        toSaveChange()
      }
      done()
    }
  })
};
const editValueCell = ({vvalue,vkey},path) => {
    let {first,second,third,fourth,fifth}=path
    const messageHtml = `
        <div class="iner-mes">
            <div class="iner-mes-item">
                <div class="s-label">维度</div>
                <div ><input type="text" id="valuekey" class="el-input__inner" value=${vkey}></div>
            </div>
            <div class="iner-mes-item">
                <div class="s-label">值</div>
                <div ><input type="text" id="valueval" class="el-input__inner"value=${vvalue}></div>
            </div>
            <div>
        </div>
        `;
    ElMessageBox({
        title: '编辑信息',
        customClass:"mes-input-form",
        message: messageHtml,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        // inputValue: value,
        dangerouslyUseHTMLString: true,
        beforeClose: (action, instance, done) => {
        if (action === 'confirm') {
                const valuekey = document.getElementById('valuekey').value;
                const valueval = document.getElementById('valueval').value;
                // 验证输入
                if (!valuekey.trim()) {
                    ElMessage.warning('请输入维度');
                    return; // 不关闭弹窗
                }
                //  || isNaN(valueval)
                if (!valueval) {
                    ElMessage.warning('请输入值');
                    return; // 不关闭弹窗
                }
                // 处理获取到的多个值
                console.log('原来来的值:', { vvalue,vkey});
                console.log('输入的值:', { valuekey,valueval});
                
                if(fourth){
                    if(vkey==valuekey){
                        baseData.value[currentview.value][0][first][second][third][fourth][valuekey]=valueval
                    }else{
                        delete baseData.value[currentview.value][0][first][second][third][fourth][vkey];
                        baseData.value[currentview.value][0][first][second][third][fourth][valuekey]=valueval
                    }
                }else{
                    console.log(baseData.value[currentview.value][0][first][second])
                    baseData.value[currentview.value][0][first][second][third]=[{}]
                    baseData.value[currentview.value][0][first][second][third][0][valuekey]=valueval
                }
                // 页面展示修改
                countInitInfo()
                // 直接保存
                toSaveChange()
        }
        done()
        }
    })
};
// 更新baseData
const updateBaseData = (inputchange) => {
    let {excelPreviewUrl,financial,economic}=JSON.parse(JSON.stringify(baseData.value));
    let { fixed_costs,variable_costs }=reverseProcessTypeDatas(JSON.parse(JSON.stringify(rowdatas.value)));
    console.log(economic)
    if(currentview.value==='financial'){
        financial[0].fixed_costs=fixed_costs
        financial[0].variable_costs=variable_costs
    }else if(currentview.value==='economic'){
        economic[0].fixed_costs=fixed_costs
        economic[0].variable_costs=variable_costs
    }
    baseData.value={
        excelPreviewUrl,financial:financial,economic:economic
    }
    // 
    console.log('baseData.value',baseData.value)
    if(inputchange=='inputchange'){
        toSaveChange()
    }
};
const toSaveChange=debounceCustom(()=>{
    console.log(baseData.value)
    // return
    updatePriceSessionView({
        entity:{
            id:dataid.value,
            data:JSON.stringify(baseData.value)
        }
    }).then((res)=>{
        if(res?.data?.updatePriceSessionView){
            ElMessage.success('修改成功')
        }
    }).catch((err)=>{
        ElMessage.error('修改失败: ' + err)
    })
})
const saveEdit= debounceCustom(()=>{
    updateBaseData('inputchange');
})
// 
const preAddData=(path) => {
    let {first,second}=path
    ElMessageBox.prompt(`请先输入名称:`, '新增', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: '',
    // inputType: 'textarea',
    inputValidator: (value) => {
      if (!value || value.trim() === '') {
        return '内容不能为空'; 
      }
      return true; 
    },
    inputErrorMessage: '请输入有效内容',
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        if(!baseData.value[currentview.value]){
            baseData.value[currentview.value]=[]
        }
        if(!baseData.value[currentview.value][0]){
            baseData.value[currentview.value][0]={
                fixed_costs:[],
                variable_costs:[],
                gross_margin_rate:{},
                volume:{}
            }
        }
        // 
        baseData.value=JSON.parse(JSON.stringify(baseData.value));
        console.log('baseData.value[currentview.value][0][first]',baseData.value[currentview.value][0][first])
        // 
        if(typeof second==='number'){//volume,gross_margin_rate
            if(!baseData.value[currentview.value][0][first]){
                baseData.value[currentview.value][0][first]=[]
            }
            baseData.value[currentview.value][0][first].splice(second+1,0,{
                name:instance.inputValue
            })
        }else if(typeof second==='string'){
            if(!baseData.value[currentview.value][0][first]){
                baseData.value[currentview.value][0][first]={}
            }
            baseData.value[currentview.value][0][first][instance.inputValue]=""
        }
        // 页面展示修改
        countInitInfo()
        // // 直接保存
        toSaveChange()
      }
      done()
    }
  })
};
// 
const preDelData=(path)=>{
    let {first,second,third,fourth}=path
    ElMessageBox.confirm('确认删除？', '删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }).then(()=>{
        if(third){
            if(typeof fourth==='number'){
                baseData.value[currentview.value][0][first][second][third].splice(fourth,1)
            }else if(typeof fourth==='string'){
                delete baseData.value[currentview.value][0][first][second][third][fourth]
            }  
        }else{
            if(typeof second==='number'){
                baseData.value[currentview.value][0][first].splice(second,1)
            }else if(typeof second==='string'){
                delete baseData.value[currentview.value][0][first][second]
            }                       
        }
        
        // 页面展示修改
        countInitInfo()
        // 直接保存
        // toSaveChange()
    }).catch(() => {
      // 取消编辑
    })
    
};
//去成本定价 
const toGetStatement=(type)=>{
  console.log("已经发起postmessage",{toolname:"财务报表分析",nowtxt:`帮我从‘${type}’ 分析这份财务数据`,sendnow:'true'})
  console.log('parent.parent',parent.parent)
        // 增加t
        // window.location.href = updateUrlHash(window.location.href, 't', (new Date().getTime()));
  parent.parent.postMessage({eventname:'triggerChangeEvent',data:{toolname:"财务报表分析",nowtxt:`帮我从‘${type}’ 分析这份财务数据`,sendnow:'true'}});
}


const startDrag = (e) => {
  isDragging = true;
  startX = e.clientX;
  startWidth = leftWidth.value;
  
  document.addEventListener('mousemove', handleDrag);
  document.addEventListener('mouseup', stopDrag);
  
  // 防止选中文本
  document.body.style.userSelect = 'none';
};

const handleDrag = (e) => {
  if (!isDragging) return;
  
  const containerWidth = leftPanel.value.parentElement.offsetWidth;
  const deltaX = e.clientX - startX;
  const deltaPercent = (deltaX / containerWidth) * 100;
  
  let newWidth = startWidth + deltaPercent;
  
  newWidth = Math.max(20, Math.min(80, newWidth));
  
  leftWidth.value = newWidth;
};

const stopDrag = () => {
  isDragging = false;
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', stopDrag);
  document.body.style.userSelect = '';
};
</script>

