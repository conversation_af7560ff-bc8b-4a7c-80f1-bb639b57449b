import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router'
import { vuexOidcCreateRouterMiddleware } from 'vuex-oidc'
import store from "@/store/index";
let contentPath = window.Global ? (window.Global.contextPath ? window.Global.contextPath : "/price") : "/price"
const routes = [{
        path: '/oidc-callback',
        name: 'OidcCallback',
        component: () =>
            import ('@/views/oidc/OidcCallback.vue')
    },
    {
        path: '/oidc-silent',
        name: 'Oidcsilent',
        component: () =>
            import ('@/views/oidc/OidcRenew.vue')
    },
    {
        path: '/',
        name: 'price',
        component: () =>
            import ('@/views/view.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/index',
        name: 'index',
        component: () =>
            import ('@/views/index.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/charts/pointelasticities',
        name: 'pointelasticities',
        component: () =>
            import ('@/views/priceelasticity/pointelasticities.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/charts/pointelasticities_edit',
        name: 'pointelasticities_edit',
        component: () =>
            import ('@/views/priceelasticity/pointelasticities_edit.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/charts/arcelasticities_edit',
        name: 'arcelasticities_edit',
        component: () =>
            import ('@/views/priceelasticity/arcelasticities_edit.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/charts/arcelasticities',
        name: 'arcelasticities',
        component: () =>
            import ('@/views/priceelasticity/arcelasticities.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/charts/mixelasticities_edit',
        name: 'mixelasticities_edit',
        component: () =>
            import ('@/views/priceelasticity/mixelasticities_edit.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/charts/mixelasticities',
        name: 'mixelasticities',
        component: () =>
            import ('@/views/priceelasticity/mixelasticities.vue'),
        meta: {
            requiresAuth: true
        },
    },

    {
        path: '/atypica',
        name: 'atypica',
        component: () =>
            import ('@/views/marketanalysis/atypica.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/competitive-dimensions',
        name: '/competitivedimensions',
        component: () =>
            import ('@/views/competitiveanalysis/competitivedimensions.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/brainstormingbotchat',
        name: '/brainstormingbotchat',
        component: () =>
            import ('@/views/competitiveanalysis/brainstormingbotchat/ai/robotAuth.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/stepview',
        name: 'stepview',
        component: () =>
            import ('@/views/stepview.vue'),
        meta: {
            requiresAuth: true
        },
    },

    {
        path: '/matchview',
        name: 'matchview',
        component: () =>
            import ('@/views/competitiveanalysis/matchview.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/simplematch',
        name: 'simplematch',
        component: () =>
            import ('@/views/competitiveanalysis/simplematch.vue'),
        meta: {
            requiresAuth: true
        },
    }, {
        path: '/simplematchv2',
        name: 'simplematchv2',
        component: () =>
            import ('@/views/competitiveanalysis/simplematchv2.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/differentiation',
        name: 'differentiation',
        component: () =>
            import ('@/views/competitiveanalysis/differentiation.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/brainstormingTopic',
        name: 'brainstormingTopic',
        component: () =>
            import ('@/views/competitiveanalysis/brainstormingTopic.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/brainstormingbot',
        name: 'brainstormingbot',
        component: () =>
            import ('@/views/competitiveanalysis/brainstormingbot.vue'),
        meta: {
            requiresAuth: true
        },
    },


    {
        path: '/marketsegments',
        name: 'marketsegments',
        component: () =>
            import ('@/views/costpricing/marketsegments.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/tezanReportCallBack',
        name: 'tezanReportCallBack',
        component: () =>
            import ('@/views/competitiveanalysis/tezan.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/financialstatements',
        name: 'financialstatements',
        component: () =>
            import ('@/views/costpricing/financialstatements.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/costPricing',
        name: 'costPricing',
        component: () =>
            import ('@/views/costpricing/costPricing.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/textedit',
        name: 'textedit',
        component: () =>
            import ('@/views/textedit.vue'),
        meta: {
            requiresAuth: true
        },
    }, {
        path: '/test',
        name: 'test',
        component: () =>
            import ('@/views/test.vue'),
        meta: {
            // requiresAuth: true
        },
    }, {
        path: '/report',
        name: 'report',
        component: () =>
            import ('@/views/report.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/manage',
        name: 'manageindex',
        component: () =>
            import ('@/views/manage/index.vue'),
        meta: {
            requiresAuth: true
        },
        children: [{
            path: '/manage/index',
            name: 'manageindexindex',
            component: () =>
                import ('@/views/manage/case.vue'),
            meta: {
                requiresAuth: true
            },
        }, {
            path: '/manage/courseview',
            name: 'courseview',
            component: () =>
                import ('@/views/manage/courseview.vue'),
            meta: {
                requiresAuth: true
            },
        }, {
            path: '/manage/scheduleClasses',
            name: 'scheduleClasses',
            component: () =>
                import ('@/views/manage/scheduleClasses.vue'),
            meta: {
                requiresAuth: true
            },
        }]
    },
    {
        path: '/shower/index',
        name: 'showerindex',
        component: () =>
            import ('@/views/showoff/index.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/case/preview',
        name: 'casepreview',
        component: () =>
            import ('@/views/preview/index.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/courseview',
        name: 'courseview1',
        component: () =>
            import ('@/views/courseview/index.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/matchviewreport',
        name: 'matchviewreport',
        component: () =>
            import ('@/views/report/matchview_report.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/differentiationreport',
        name: 'differentiationreport',
        component: () =>
            import ('@/views/report/differentiation_report.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/atypicareport',
        name: 'atypicareport',
        component: () =>
            import ('@/views/report/atypica_report.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/marketsegmentsreport',
        name: 'marketsegmentsreport',
        component: () =>
            import ('@/views/report/marketsegments_report.vue'),
        meta: {
            requiresAuth: true
        },
    },
    {
        path: '/costpricingreport',
        name: 'costpricingreport',
        component: () =>
            import ('@/views/report/costpricing_report.vue'),
        meta: {
            requiresAuth: true
        },
    },

]
const router = createRouter({
    history: createWebHistory(contentPath),
    routes
})
const rsearch = window.location.search
router.beforeEach((to, from, next) => {

    const requiresAuth = to.matched.some(record => record.meta.requiresAuth);
    if (requiresAuth) {
        const middle = vuexOidcCreateRouterMiddleware(store, 'oidcStore');
        middle(to, from, next);
    } else {
        next();
    }
});
export default router